package com.bukuwarung.activities.onboarding.form

import androidx.lifecycle.*
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.ViewModelEvent
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.RiskUseCase
import com.bukuwarung.payments.data.model.BookValidationRequest
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class FormViewModelFactory @Inject constructor(
    private val riskUseCase: RiskUseCase
) :
    ViewModelProvider.Factory {
    override fun <T : ViewModel> create(modelClass: Class<T>): T =
        FormViewModel(riskUseCase) as T
}

class FormViewModel @Inject constructor(
    private val riskUseCase: RiskUseCase
) : BaseViewModel() {
    private val _formStep = MutableLiveData(0)
    val formStep: LiveData<Int> = _formStep
    var onboardingData: OnboardingData = OnboardingData()
    val onboardingDataLive: MutableLiveData<OnboardingData> = MutableLiveData()
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus

    val formOption = RemoteConfigUtils.OnBoarding.getOnBoardingFormsAsList()

    sealed class Event : ViewModelEvent {
        data class BookValidationError(val bookName: String?) : Event()
        data class BookNameInvalidCharError(val bookName: String?) : Event()
        object BookValidationSuccess: Event()
    }

    fun formAction(formType: FormType, formValue: String, formValueId: String = "") = viewModelScope.launch {
        val nextStep = (_formStep.value ?: 0) + 1
        if (_formStep.value in formOption.indices) {
            when (formType) {
                FormType.BusinessName -> setBusinessName(formValue)
                FormType.BusinessCategory -> setBusinessCategory(formValue,formValueId)
                FormType.UsageGoal -> setUsageGoal(formValue,formValueId)
                FormType.UsagePast -> setUsagePast(formValue)
            }
        }
        if(nextStep !in formOption.indices){
            submitForm()
        }
    }

    private fun setBusinessName(businessName:String) {
        if (Utility.isValidName(businessName)) {
            checkBusinessNameValidation(businessName)
        }else{
            eventStatus.postValue(Event.BookNameInvalidCharError(businessName ?: ""))
        }
    }

    private fun setBusinessCategory(businessCategory: String, businessCategoryId:String) {
        val nextStep = (_formStep.value ?: 0) + 1
        _formStep.postValue(nextStep)
        onboardingData.businessCategory = businessCategory
        onboardingData.businessCategoryId = businessCategoryId
        val prop = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.PAGE_NAME, AnalyticsConst.BUSINESS_CATEGORY_PAGE)
            put(AnalyticsConst.TYPE, businessCategoryId)
        }
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
            prop
        )
    }

    private fun setUsagePast(pastUsage:String) {
        val nextStep = (_formStep.value ?: 0) + 1
        _formStep.postValue(nextStep)
        onboardingData.pastUsage = pastUsage
        val prop = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.PAGE_NAME, AnalyticsConst.USAGE_PAST_PAGE)
            put(AnalyticsConst.USAGE_PAST_PAGE, pastUsage)
        }
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
            prop
        )
    }

    private fun setUsageGoal(goalUsage: String, tabId: String) {
        val nextStep = (_formStep.value ?: 0) + 1
        _formStep.postValue(nextStep)
        onboardingData.goalUsage = goalUsage
        onboardingData.tabId = tabId.toInt()
        val prop = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.PAGE_NAME, AnalyticsConst.USAGE_GOAL_PAGE)
            put(AnalyticsConst.USAGE_GOAL_PAGE, goalUsage)
        }
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
            prop
        )
    }

    fun checkBusinessNameValidation(name: String?) =
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                var bookValidation: ApiResponse<Any>? = null
                coroutineScope {
                    name?.let { name ->
                        launch {
                            bookValidation =
                                riskUseCase.validateBookName(BookValidationRequest(name))
                            bookValidation?.let {
                                when (it) {
                                    is ApiErrorResponse -> {
                                        if (it.statusCode == 422) {
                                            val blockedBook = PaymentUtils.parseBlacklistedBookName(it.errorMessage)
                                            eventStatus.postValue(Event.BookValidationError(blockedBook ?: name))
                                        }
                                    }
                                    is ApiSuccessResponse -> {
                                        val nextStep = (_formStep.value ?: 0) + 1
                                        _formStep.postValue(nextStep)
                                        if (name != null) {
                                            onboardingData.businessName = name
                                        }
                                        val prop = AppAnalytics.PropBuilder().apply {
                                            put(AnalyticsConst.PAGE_NAME, AnalyticsConst.BUSINESS_NAME_PAGE)
                                            put(AnalyticsConst.BUSINESS_NAME_PAGE, name)
                                        }
                                        AppAnalytics.trackEvent(
                                            AnalyticsConst.EVENT_CONTINUE_BUSINESS_DETAIL_DIALOGUE,
                                            prop
                                        )
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

    private fun submitForm() {
        onboardingDataLive.postValue(onboardingData)
    }
}