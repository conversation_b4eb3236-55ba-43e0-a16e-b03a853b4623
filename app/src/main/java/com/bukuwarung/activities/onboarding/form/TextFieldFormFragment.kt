package com.bukuwarung.activities.onboarding.form

import android.content.res.ColorStateList
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.databinding.TextFieldFormFragmentBinding
import com.bukuwarung.utils.*
import java.util.*
import javax.inject.Inject

class TextFieldFormFragment : BaseFragment() {
    private lateinit var binding: TextFieldFormFragmentBinding
    @Inject
    lateinit var vmFactory: FormViewModelFactory
    private val vm: FormViewModel by activityViewModels { vmFactory }
    private var requireReferral: Boolean = false
    private var bizzName: String = ""
    private var referralCode: String = ""

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?): View? {
        binding = TextFieldFormFragmentBinding.inflate(inflater)

        return binding.root
    }

    override fun setupView(view: View) {
        with(requireArguments()) {
            binding.apply {
                requireReferral = getBoolean(REQUIRE_REFERRAL, false)
                tilReferral.visibility = requireReferral.asVisibility()
            }
        }
        binding.tilBizzName.requestFocus()
        binding.apply {
            tvBizzName.afterTextChanged { txt ->
                bizzName = txt
                validateForm()
                binding.tvBusinessNameError.hideView()
                binding.tilBizzName.hintTextColor = ColorStateList.valueOf(
                    ContextCompat.getColor(
                        binding.tilBizzName.context,
                        R.color.colorPrimary
                    )
                )
                binding.tilBizzName.boxStrokeColor =
                    ContextCompat.getColor(binding.tilBizzName.context, R.color.colorPrimary)
            }

            tvReferral.afterTextChanged { txt ->
                referralCode = txt
                validateForm()
            }

            btnAction.setOnClickListener {
                if (Utility.hasInternet()) {
                    vm.formAction(FormType.BusinessName, tvBizzName.text?.trim().toString())
                } else {
                    Toast.makeText(
                        requireContext(),
                        getString(R.string.no_internet_error),
                        Toast.LENGTH_SHORT
                    ).show()
                }
                InputUtils.hideKeyBoardWithCheck(activity)
            }
        }
    }

    private fun validateForm() {
        binding.btnAction.isEnabled = if (requireReferral) {
            bizzName.trim().isNotEmpty() && referralCode.trim().isNotEmpty()
        } else {
            bizzName.trim().isNotEmpty()
        }
    }

    override fun subscribeState() {
        vm.observeEvent.observe(this) {
            when (it) {
                is FormViewModel.Event.BookValidationError -> {
                    binding.tvBusinessNameError.showView()
                    binding.tvBusinessNameError.text =
                        getString(
                            R.string.change_name_of_business_msg_2,
                            it.bookName?.uppercase(Locale.getDefault())?.trim()
                        )
                    binding.tilBizzName.boxStrokeColor =
                        ContextCompat.getColor(binding.tilBizzName.context, R.color.red_80)
                    binding.tilBizzName.hintTextColor = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.tilBizzName.context,
                            R.color.red_80
                        )
                    )
                }
                is FormViewModel.Event.BookNameInvalidCharError -> {
                    binding.tvBusinessNameError.showView()
                    binding.tvBusinessNameError.text ="Mohon tidak menggunakan karakter spesial dan emoji \n" +
                                "(^\$*.[]{}()?-\"!#%&/\\,><':;|_~`) \uD83D\uDE03"
                    binding.tilBizzName.boxStrokeColor =
                        ContextCompat.getColor(binding.tilBizzName.context, R.color.red_80)
                    binding.tilBizzName.hintTextColor = ColorStateList.valueOf(
                        ContextCompat.getColor(
                            binding.tilBizzName.context,
                            R.color.red_80
                        )
                    )
                }
            }
        }
    }

    companion object {
        private const val REQUIRE_REFERRAL = "require_referral"
        fun getInstance(requireReferral: Boolean = false): TextFieldFormFragment {
            val bundle = Bundle().apply {
                putBoolean(REQUIRE_REFERRAL, requireReferral)
            }

            return TextFieldFormFragment().apply {
                arguments = bundle
            }
        }
    }
}