package com.bukuwarung.activities.payment

import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.homepage.data.BodyBlock
import com.bukuwarung.activities.homepage.data.FragmentBlock
import com.bukuwarung.activities.homepage.data.FragmentBodyBlock
import com.bukuwarung.activities.homepage.view.HomeBnplFragment
import com.bukuwarung.activities.homepage.view.NoInternetAvailableDialog
import com.bukuwarung.activities.payment.PaymentBannerAdapter.Companion.KYC_BANNER_TYPE
import com.bukuwarung.activities.payment.PaymentBannerAdapter.Companion.QRIS_BANNER_TYPE
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.commonview.view.BukuTileView
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.EVENT_PPOB_REMINDERS
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.TYPE_PAYMENT_IN
import com.bukuwarung.constants.PaymentConst.TYPE_PAYMENT_OUT
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.TabLayoutPaymentBinding
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.PaymentHistoryDetailsActivity
import com.bukuwarung.payments.PaymentTutorialBottomSheet
import com.bukuwarung.payments.bottomsheet.KycKybBottomSheet
import com.bukuwarung.payments.bottomsheet.PaymentOptionsBottomSheet
import com.bukuwarung.payments.bottomsheet.SaldoLimitsBottomSheet
import com.bukuwarung.payments.bottomsheet.SaldoTutorialBottomSheet
import com.bukuwarung.payments.constants.*
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.deeplink.handler.SaldoSignalHandler
import com.bukuwarung.payments.history.OrderHistoryActivity
import com.bukuwarung.payments.history.OrderHistoryAdapter
import com.bukuwarung.payments.history.OrderListCallback
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.ppob.base.listeners.PpobProductsListener
import com.bukuwarung.payments.ppob.reminders.view.ReminderActivity
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.qris.QrisKYBPendingBottomSheet
import com.bukuwarung.payments.saldo.TopupSaldoActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.widget.ErrorBottomSheet
import com.bukuwarung.payments.widget.QrisVerificationView
import com.bukuwarung.payments.widget.SaldoLimitsView
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.prefs.PreferencesManager
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.tutor.view.OnboardingWidget.Companion.createInstance
import com.bukuwarung.utils.*
import com.bukuwarung.utils.RemoteConfigUtils.getAppText
import com.github.razir.progressbutton.bindProgressButton
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.reflect.TypeToken
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject


class PaymentTabFragment : BaseFragment(),
    OnboardingWidget.OnboardingWidgetListener,
    SaldoTutorialBottomSheet.SaldoTutorialListener,
    PpobProductsListener, OrderListCallback,
    PaymentBannerAdapter.Callback, QrisVerificationView.Callback,
    PaymentTutorialBottomSheet.Callback, SaldoLimitsView.Callback,
    Navigator {

    interface PaymentTabFragmentListener {
        fun handleNormalBackPressedFromPayment()
        fun callLoginFromPaymentTab(entryPoint: String)
        fun setKycInfo()
    }
    private var listener: PaymentTabFragmentListener? = null
    private lateinit var binding: TabLayoutPaymentBinding
    private var onboardingWidget: OnboardingWidget? = null
    private val scope = MainScope()
    private var afterRestoreState = false
    private var isEmptyState: Boolean = false
    private var snackbarHandler: Handler? = null
    var openShowMore: Boolean = false

    private var from: String? = null

    companion object {
        private const val PAGE_SIZE = 10

        fun createIntent(from: String?): PaymentTabFragment {
            val fragment = PaymentTabFragment()
            fragment.arguments = Bundle().apply {
                putString("from", from)
            }
            return fragment
        }
    }
    private var emptyStateTextIndex: Int = -1
    private lateinit var emptyStateTextArray : Array<String>

    @Inject
    lateinit var viewModel: PaymentTabViewModel

    @Inject
    lateinit var neuro: Neuro

    private var adapter: OrderHistoryAdapter? = null
    private var preferenceManager: PreferencesManager? = null
    private val pagerHandler by lazy { Handler() }
    private val paymentBannerAdapter: PaymentBannerAdapter by lazy { PaymentBannerAdapter(this, this) }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {
        binding = TabLayoutPaymentBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is PaymentTabViewModel.State.SetBookData -> initBookData(it.bookId, it.businessName)
                is PaymentTabViewModel.State.SetSummaryData -> setSummaryData(
                    it.summaryResponse,
                    it.bookId
                )
                is PaymentTabViewModel.State.SetPaymentList -> setPaymentList(it.list)
                PaymentTabViewModel.State.ShowPpobCoachmark -> showPpobCoachmark()
                PaymentTabViewModel.State.showEmptyStateInfo -> changeEmptyStateWordings(true)
                PaymentTabViewModel.State.ShowSaldoCoachmark -> showSaldoCoachmark()
                is PaymentTabViewModel.State.IsPpobAvailable -> {
                    trackAndStartPpob(it.fragmentBodyBlock)
                }
                is PaymentTabViewModel.State.GoToCreatePayment -> {
                    showPaymentOptionsIfWhitelisted()
                }
                is PaymentTabViewModel.State.IsCRMReminderAvailable -> {
                    AppAnalytics.trackEvent(EVENT_PPOB_REMINDERS, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN)
                    )
                    startActivity(
                            ReminderActivity.createIntent(
                                    requireContext()
                            )
                    )
                }
                is PaymentTabViewModel.State.BannerList -> handleBannerList(it.bannerList)
                is PaymentTabViewModel.State.SetPaymentInfoSaving -> setSavingPaymentInfo(it.paymentMetadata)
                is PaymentTabViewModel.State.ShowKybPendingBottomSheet -> QrisKYBPendingBottomSheet().show(childFragmentManager, QrisKYBPendingBottomSheet.TAG)
                else -> {}
            }
        }
        subscribeSingleLiveEvent(viewModel.saldoState) { state ->
            when (state) {
                is PaymentTabViewModel.State.SaldoState -> {
                    state.saldoData?.let {
                        binding.saldoLimitView.setView(it, true, this)
                    }
                }
                else -> {}
            }
        }
        viewModel.viewState.observe(this) {
            if (it.showLoading) {
                hideViews()
                return@observe
            }
            binding.loadingStateGroup.hideView()
            binding.swipeRefresh.isRefreshing = false
            binding.errorStateGroup.visibility = it.showError.asVisibility()
            isEmptyState = it.showEmptyState
            changeEmptyStateWordings(it.showEmptyState)
            if (!RemoteConfigUtils.shouldShowBnplFragment()) handleSaldoResponse(it) else binding.saldoGroup.hideView()
            if (it.showEmptyState) {
                showEmptyStateUI()
            } else {
                binding.swipeRefresh.showView()
                binding.trxCountGroup.showView()
                binding.coordinatorLayout.showView()
            }
        }
//        viewModel.qrisDataLive.observe(this) {
//            handleQrisResponse(viewModel.isQrisAccountBlocked())
//        }
//        viewModel.qrisDeactivatedGroup.observe(this) {
//            if (it.status == WhitelistGroupStatus.ENABLED) {
//                // User is part of deactivated QRIS group
//                binding.btnQrisDetail.setDeactivatedView()
//            }
//        }
        viewModel.event.observe(viewLifecycleOwner) {
            when (it) {
                is PaymentTabViewModel.Event.LinkedItemsFetched -> updateLinkedOrders(it.adapterPos)
                is PaymentTabViewModel.Event.ApiError -> {
                    handleError(it.errorMessage)
                    updateLinkedOrders(it.adapterPos)
                }
            }
        }
//        AppAnalytics.setUserProperty(AnalyticsConst.PHONE, SessionManager.getInstance().userId)

        bindProgressButton(binding.addPaymentBtn)
    }

    private fun updateLinkedOrders(adapterPos: Int) {
        adapter?.notifyItemChanged(adapterPos)
    }

    private fun changeEmptyStateWordings(isEmptyState: Boolean) {
        ComponentUtil.setVisible(binding.emptyStateInfo, isEmptyState)
        if (PaymentPrefManager.getInstance().getTransactionHistoryFeatureFlag()) {
            return
        }
        if(isEmptyState){
            emptyStateTextIndex++
            emptyStateTextIndex %= 10
            //before changing the text need to show the effect as we are transitioning to other text
            showShimmerEffect(true)
            Handler().postDelayed(Runnable {
                kotlin.run {
                    showShimmerEffect(false)
                }
            }, 1000)
            // code to change the text and call itself
            binding.tvEmptyState.text = emptyStateTextArray[emptyStateTextIndex]
            binding.ivEmptyState.setImageDrawable(ContextCompat.getDrawable(requireContext(), if (emptyStateTextIndex % 2 == 0) R.drawable.ic_payment_0_hi else R.drawable.ic_payment_0_money))
        }
    }

    private fun showShimmerEffect(showShimmerEffect: Boolean){
        ComponentUtil.setVisible(binding.emptyStateInfoLayout,!showShimmerEffect)
        ComponentUtil.setVisible(binding.emptyStateInfoEffect,showShimmerEffect)
    }

    /**
     * Hides saldo features if it is not enabled for the user.
     */
    private fun handleSaldoResponse(it: PaymentTabViewModel.ViewState) {
        with(binding) {
            if (it.saldoEnabled.isTrue) {
                saldoGroup.visibility = (it.saldoBalance != null).asVisibility()
                layoutSaldoCoachmark.visibility = (it.saldoBalance != null).asVisibility()
                tvSaldoBalance.text =
                    if (it.saldoBalance != null) Utility.formatAmount(it.saldoBalance)
                    else getString(R.string.load_failed)
                it.saldoBalance?.let {
                    AppAnalytics.setUserProperty(AnalyticsConst.CURRENT_WALLET_BALANCE_SALDO, it)
                }
                if (it.saldoBalance.orNil == 0.0 && it.cashbackBalance.orNil == 0.0) {
                    tvCashbackBalance.text = getString(R.string.make_credit_and_sales)
                    tvCashbackBalance.setOnClickListener { topupSaldoInitiated() }
                } else {
                    tvCashbackBalance.text = getString(
                        R.string.saldo_bonus_x, Utility.formatAmount(it.cashbackBalance)
                    )
                    tvCashbackBalance.singleClick { openCashbackHistory() }
                }
            } else {
                saldoGroup.hideView()
            }
        }
    }

    /**
     * Show QRIS access button if user is whitelisted and qris book is current active book
     */
    private fun handleQrisResponse(blockedQrisBank: Boolean) {
        binding.btnQrisDetail.apply {
            hideViews()
            setView(childFragmentManager, AnalyticsConst.PEMBAYARAN, blockedQrisBank)
        }
//        viewModel.qrisDeactivatedGroup.value?.let {
//            if (it.status == WhitelistGroupStatus.ENABLED) {
//                // User is part of deactivated QRIS group
//                binding.btnQrisDetail.setDeactivatedView()
//            }
//        }
    }

    private fun setKycInfo() {
        when(PaymentPrefManager.getInstance().getKycTier()) {
            KycTier.ADVANCED -> binding.reshowTutorialIcon.changeIcon(R.drawable.ic_kyc_badge_premium)
            KycTier.SUPREME -> binding.reshowTutorialIcon.changeIcon(R.drawable.ic_kyc_badge_priority)
            else -> binding.reshowTutorialIcon.changeIcon(R.drawable.ic_kyc_badge_standard)
        }
        listener?.setKycInfo()
    }

    private fun shouldRemoveKycBanner(banner: PaymentBannerInfoResponse): Boolean {
        if (banner.meta?.bannerType != KYC_BANNER_TYPE) return false
        val finalStatus = viewModel.qrisData?.finalStatus
        // Hide KYC widget/banner if qris application is in process/verified
        if (finalStatus != null) return true
        val kycTier = PaymentPrefManager.getInstance().getKycTier()
        // If both KYC and KYB are approved then we show banner for 7 days,
        // If KYC is approved but KYB is pending, we show banner for 7 days,
        // otherwise we always show the banner for kyc/kyb info.
        val kybStatus = PaymentPrefManager.getInstance().getKybStatus()
        return when {
            kycTier.isVerified() && kybStatus?.isVerified().isTrue -> viewModel.hideAccountVerifiedBanner()
            kycTier.isVerified() && kybStatus.isInitial().isTrue -> viewModel.hideKybPendingBanner()
            else -> false
        }
    }

    private fun shouldRemoveQrisBanner(banner: PaymentBannerInfoResponse): Boolean {
        if (banner.meta?.bannerType != QRIS_BANNER_TYPE) return false
        val qrisBookId = viewModel.qrisData?.metadata?.qris?.paymentBookId
        // We need to remove banner from other books
        val bookId = SessionManager.getInstance().businessId
        if (qrisBookId != null && qrisBookId != bookId) return true
        if (viewModel.qrisData?.finalStatus == QrisAndKycStatus.VERIFIED.name) return true
        return false
    }

    private fun handleBannerList(bannerList: List<PaymentBannerInfoResponse>) {
        val bannerFilteredList: MutableList<PaymentBannerInfoResponse> = mutableListOf()
        bannerList.forEach {
            if (shouldRemoveKycBanner(it) || shouldRemoveQrisBanner(it)) {
            } else {
                bannerFilteredList.add(it)
            }
        }
        val kycBanner = bannerList.find { KYC_BANNER_TYPE == it.meta?.bannerType }
        with(binding) {
            if (bannerFilteredList.isNullOrEmpty()) {
                vpPaymentBanner.hideView()
                tbPaymentBanner.hideView()
            } else {
                vpPaymentBanner.showView()
                if (bannerFilteredList.size > 1) {
                    tbPaymentBanner.showView()
                } else {
                    tbPaymentBanner.hideView()
                }
            }
        }
        paymentBannerAdapter.setItem(bannerFilteredList, viewModel.qrisData)
        setKycInfo()
    }

    private fun setSavingPaymentInfo(paymentMetadata: PaymentMetadata) {
        binding.paymentInfoMessage.setPaymentTransactionsInfo(paymentMetadata)
    }

    private fun hideViews() {
        binding.errorStateGroup.hideView()
        binding.emptyStateInfo.hideView()
        binding.loadingStateGroup.showView()
        binding.coordinatorLayout.hideView()
    }

    private fun setPaymentList(list: List<PaymentHistory>) {
        binding.swipeRefresh.isRefreshing = false
        if (adapter == null || afterRestoreState) {
            initAdapter()
            afterRestoreState = false
        }
        val ordersData = arrayListOf<OrderHistoryData>()
        list.forEach {
            ordersData.add(
                OrderHistoryData(
                    orderData = it,
                    formattedDate = null,
                    timestamp = null,
                    viewType = OrderHistoryAdapter.Companion.ViewType.ORDER
                )
            )
        }
        ordersData.add(
            OrderHistoryData(
                orderData = null,
                formattedDate = null,
                timestamp = null,
                viewType = OrderHistoryAdapter.Companion.ViewType.SEE_ALL_ORDERS
            )
        )
        adapter?.setData(ordersData)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val pageMarginPx = resources.getDimensionPixelOffset(R.dimen._8dp)
        val offsetPx = resources.getDimensionPixelOffset(R.dimen._8dp)
        viewModel.getTransactionFeatureFlag()
        with(binding.vpPaymentBanner) {
            clipToPadding = false
            clipChildren = false
            offscreenPageLimit = 3
            layoutParams.height = ((activity?.let { getScreenWidth(it) } ?: 0) * 0.22).toInt()
            setPreview(offsetPx, pageMarginPx)
            adapter = paymentBannerAdapter
            registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
                override fun onPageSelected(position: Int) {
                    super.onPageSelected(position)
                    // Somehow code reached here even when itemCount was zero
                    if (paymentBannerAdapter.itemCount == 0) return
                    /*
                        Need to clear old runnable, sometimes multiple runnable gets added to the
                        message queue which scrolls banners relentlessly
                     */
                    pagerHandler.removeMessages(0)
                    val runnable = Runnable {
                        if (paymentBannerAdapter.itemCount > 0 ) {
                            currentItem = (currentItem + 1) % paymentBannerAdapter.itemCount
                        }
                    }
                    pagerHandler.postDelayed(runnable, RemoteConfigUtils.getProfileBannerAutoScrollTime())
                }

                override fun onPageScrollStateChanged(state: Int) {
                    super.onPageScrollStateChanged(state)
                    /**
                     * The user swiped forward or back and we need to
                     * invalidate the previous handler.
                     */
                    if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                        pagerHandler.removeMessages(0)
                    }
                }

                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                }
            })
        }
        TabLayoutMediator(binding.tbPaymentBanner, binding.vpPaymentBanner) { tab, position ->
        }.attach()

        // Starting bank accounts sync process
        val activeBookId = SessionManager.getInstance().businessId
        Utilities.safeLet(context, activeBookId) { context, bookId ->
            PaymentUtils.syncCustomerBankAccounts(context, bookId)
        }
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is PaymentTabFragmentListener) {
            listener = context
        }
        preferenceManager = PreferencesManager(context)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
        afterRestoreState = savedInstanceState != null
    }

    private fun initAdapter() {
        adapter = OrderHistoryAdapter(this, viewModel.linkedOrdersMap)
        binding.paymentRv.adapter = adapter
        binding.paymentRv.layoutManager = LinearLayoutManager(context)
    }

    private fun showEmptyStateUI() {
        with(binding) {
            coordinatorLayout.showView()
            swipeRefresh.hideView()
            trxCountGroup.hideView()
            clEmptyStateStatic.hideView()
            if (PaymentPrefManager.getInstance().getTransactionHistoryFeatureFlag()) {
                trxCountGroup.showView()
                clEmptyStateStatic.showView()
            }
        }
    }

    private fun setSummaryData(summaryResponse: PaymentSummaryResponse?, bookId: String?) {
        if (summaryResponse == null) {
            binding.incomeTotalTxt.text = getString(R.string.load_failed)
            binding.expenseTotalTxt.text = getString(R.string.load_failed)
            return
        }
        binding.incomeTotalTxt.text = Utility.formatAmount(summaryResponse.amount?.paymentIn)
        binding.expenseTotalTxt.text = Utility.formatAmount(summaryResponse.amount?.paymentOut)
        binding.trxCountGroup.showView()
        binding.summaryInView.singleClick {
            startHistoryActivity(bookId, TYPE_PAYMENT_IN)
        }
        binding.summaryOutView.singleClick {
            startHistoryActivity(bookId, TYPE_PAYMENT_OUT)
        }
        FeaturePrefManager.getInstance().paymentTransactionCount = summaryResponse.countAll ?: 0
    }

    private fun initBookData(bookId: String?, businessName: String?) {
        with(binding) {
            addPaymentBtn.setSingleClickListener {
                if (context != null) {
                    checkGuestUser {
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_BUAT_PEMBAYARAN_CLICKED)
                        viewModel.onCreatePaymentClicked()
                    }
                }
            }

            screenTitle.text = businessName
            if (adapter == null) initAdapter()
            seeAllTxt.singleClick {
                val prop = AppAnalytics.PropBuilder().put(
                    AnalyticsConst.ENTRY_POINT2,
                    AnalyticsConst.SEE_ALL
                )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_HISTORY, prop)
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_HISTORY, requireActivity())
                startHistoryActivity(bookId)
            }
            btnTopup.setSingleClickListener {
                topupSaldoInitiated()
            }
            ivSaldoInfo.setOnClickListener {
                SaldoTutorialBottomSheet.createInstance()
                    .show(childFragmentManager, SaldoTutorialBottomSheet.TAG)
            }
            ivSaldoWallet.singleClick {
                openSaldoHistory()
            }
            tvSaldoBalance.singleClick {
                openSaldoHistory()
            }
        }
    }

    private fun openSaldoHistory() {
        startActivity(
            OrderHistoryActivity.createIntent(
                requireContext(),
                SessionManager.getInstance().businessId,
                PaymentConst.HISTORY_TABS.SALDO
            )
        )
    }

    private fun openCashbackHistory() {
        startActivity(
            OrderHistoryActivity.createIntent(
                requireContext(),
                SessionManager.getInstance().businessId,
                PaymentConst.HISTORY_TABS.SALDOBONUS
            )
        )
    }

    private fun startHistoryActivity(bookId: String?, paymentType: Int = -1) {
        if (context != null && bookId != null) {
            val productFilters = when (paymentType) {
                TYPE_PAYMENT_IN -> arrayListOf(PaymentConst.TYPE_PAY_IN)
                TYPE_PAYMENT_OUT -> arrayListOf(PaymentConst.TYPE_PAY_OUT)
                else -> null
            }
            startActivity(
                OrderHistoryActivity.createIntent(
                    requireContext(),
                    bookId,
                    productFilters = productFilters
                )
            )
        }
    }

    fun onBackPressed() {
        if (onboardingWidget != null && onboardingWidget!!.isShown) {
            onboardingWidget!!.dismiss(
                isFromButton = false,
                isFromCloseButton = false,
                isFromOutside = true
            )
        } else if (activity != null && listener != null) {
            listener?.handleNormalBackPressedFromPayment()
        } else if (activity != null) {
            requireActivity().finish()
        }
    }

    override fun setupView(view: View) {
        viewModel.handleOnCreateView()
        binding.apply {
            swipeRefresh.setOnRefreshListener {
                viewModel.handleOnResume()
            }
            showPpobCategories()
            showBnplSaldoView()
            binding.menuIcon.setOnClickListener {
                if (activity != null) {
                    (activity as MainActivity).handleSideMenuIconClick()
                }
            }
            val businessRepository = BusinessRepository.getInstance(context)
            val bookEntity = businessRepository.getBusinessByIdSync(User.getBusinessId())
            if (bookEntity != null) {
                val businessName: String = bookEntity.businessName.orEmpty()
                if (Utility.hasBusinessName(businessName)) {
                    screenTitle.text = businessName
                } else {
                    screenTitle.text = ""
                }
            }
            reshowTutorialIcon.setOnClickListener {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_ADD_BANK_ACCOUNT)) {
                    PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.PAYMENTS)
                } else {
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_PAYMENT_TUTORIAL_OPEN, AppAnalytics.PropBuilder().put(
                            AnalyticsConst.TYPE,
                            AnalyticsConst.BANNER
                        ), true, false, false
                    )
                    PaymentTutorialBottomSheet.createInstance()
                        .show(childFragmentManager, PaymentTutorialBottomSheet.TAG)
                }
            }
            ivRevisitCoachmark.setOnClickListener {
                showPpobCoachmark()
            }
            btnPaymentDown.setOnClickListener {
                viewModel.handleOnResume()
            }
        }
        emptyStateTextArray = resources.getStringArray(R.array.payment_0_heading)
        binding.ivArrow.setOnClickListener {
            AppAnalytics.trackEvent(PaymentConst.PAYMENT_ZERO_ANALYTICS_LIST[emptyStateTextIndex])
            showPaymentsCoachmark()
        }
    }

    private fun showPpobCategories(){
        val gson: Gson = GsonBuilder().create()

        val fragmentType = object : TypeToken<FragmentBlock>() {}.type
        val fragmentData = gson.fromJson<FragmentBlock>(
            RemoteConfigUtils.PembayaranTabConfig.getPaymentsFragmentData(),
            fragmentType
        )

        val type = object  : TypeToken<FragmentBodyBlock>() {}.type
        val bodyContents = RemoteConfigUtils.PembayaranTabConfig.getPaymentsBodyBlock(fragmentData.body_block_name!!)

        val bodyData = gson.fromJson<FragmentBodyBlock>(bodyContents, type)
        val fragment = BukuTileView.createIntent(bodyData, fragmentData, isPaymentScreen = true,
            listener = this, openShowMore)

        val ppoblayout = binding.ppobFrameLayout

        if (ppoblayout.childCount > 0) {
            ppoblayout.removeAllViews()
        }

        val transaction = activity?.supportFragmentManager?.beginTransaction()
        transaction?.add(ppoblayout.id, fragment, fragmentData.category)?.commitAllowingStateLoss()
    }

    fun setShowMore(showMore: Boolean) {
        openShowMore = showMore
    }

    private fun showBnplSaldoView() {
        binding.bnplSaldoLayout.visibility = RemoteConfigUtils.shouldShowBnplFragment().asVisibility()
        val fragment = HomeBnplFragment.createIntent(AnalyticsConst.PEMBAYARAN)
        val layout = binding.bnplSaldoLayout
        if (layout.childCount > 0) {
            layout.removeAllViews()
        }
        val transaction = childFragmentManager.beginTransaction()
        transaction.add(layout.id, fragment).commitAllowingStateLoss()
    }

    private fun checkGuestUser(action: () -> Unit) {
        if (SessionManager.getInstance().isGuestUser) {
            listener?.callLoginFromPaymentTab("")
        } else {
            action.invoke()
        }
    }

    private fun showPaymentsCoachmark() {
        onboardingWidget = createInstance(requireActivity(), this@PaymentTabFragment,
                OnboardingPrefManager.TUTORIAL_PEMBAYARAN_CTA, binding.addPaymentBtn, R.drawable.onboarding_cool, getString(R.string.payments_coackmark_title),
                getString(R.string.payments_coachmark_message), "", FocusGravity.CENTER, ShapeType.ROUND_RECT,
                1, 1, true, true, 0)
    }

    private fun showSaldoCoachmark(){
        if (binding.layoutSaldoCoachmark.visibility != View.VISIBLE) return
        scope.launch {
            if(activity == null || requireActivity().isFinishing) return@launch
            onboardingWidget = createInstance(
                    requireActivity(),
                    this@PaymentTabFragment,
                    OnboardingPrefManager.TUTORIAL_SALDO_CTA,
                    binding.layoutSaldoCoachmark,
                    R.drawable.onboarding_cool,
                    getString(R.string.saldo_coackmark_title),
                    getString(R.string.saldo_coachmark_message),
                    getString(R.string.try_feature),
                    FocusGravity.CENTER,
                    ShapeType.RECTANGLE_FULL,
                    1,
                    1,
                    true,
                    true,
                    0
            )
        }
    }

    fun setSubmittedKyc() {
        if (this::viewModel.isInitialized) {
            viewModel.getQrisAndPaymentMetaData(
                PaymentPrefManager.getInstance().getQrisFeatureFlag()
            )
        }
    }

    fun showQrisSuccess() {
        snackbarHandler = Handler(Looper.getMainLooper())
        snackbarHandler?.postDelayed({
            showQrisSuccessSnackbar()
        }, 500)
    }

    fun showKybSuccess() {
        KycKybBottomSheet.createInstance(
            KycKybBottomSheet.UseCase.KYB_SUBMITTED, entryPoint = AnalyticsConst.PAYMENTS
        ).show(childFragmentManager, KycKybBottomSheet.TAG)
    }

    fun showAppealFlowSuccess() {
        snackbarHandler = Handler(Looper.getMainLooper())
        snackbarHandler?.postDelayed({
            showAppealFlowSuccessSnackbar()
        }, 500)
    }

    fun showSuccess(message: String) {
        snackbarHandler = Handler(Looper.getMainLooper())
        snackbarHandler?.postDelayed({
            showSuccessSnackbar(message)
        }, 500)
    }

    private fun showQrisSuccessSnackbar() {
        if (this::binding.isInitialized) {
            binding.tvSuccessMessage.apply {
                text = getAppText().qrisSuccessMessage ?: getString(R.string.qris_submit_success)
                setDrawableRightListener { hideView() }
                showView()
            }
        }
    }

    private fun showAppealFlowSuccessSnackbar() {
        if (this::binding.isInitialized) {
            binding.tvSuccessMessage.apply {
                text = getAppText().appealFlowSubmittedText ?: getString(R.string.matching_appeal_document_submitted)
                setDrawableRightListener { hideView() }
                showView()
            }
        }
    }

    private fun showSuccessSnackbar(message: String) {
        if (this::binding.isInitialized) {
            binding.tvSuccessMessage.apply {
                text = message
                setDrawableRightListener { hideView() }
                showView()
            }
        }
    }

    private fun showPpobCoachmark() {
        if (binding.layoutPpob.visibility != View.VISIBLE) return
        scope.launch {
            delay(200)
            if(activity == null || requireActivity().isFinishing) return@launch
            onboardingWidget = createInstance(
                requireActivity(),
                this@PaymentTabFragment,
                OnboardingPrefManager.TUTORIAL_PPOB_INTRODUCTION,
                binding.layoutPpobCoachmark,
                R.drawable.onboarding_cool,
                getString(
                    R.string.this_new_feature
                ),
                getString(R.string.onboarding_ppob),
                getString(R.string.okay),
                FocusGravity.CENTER,
                ShapeType.RECTANGLE_FULL,
                1,
                1,
                true,
                true,
                0
            )
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.handleOnResume()
        viewModel.getReminderFlag()
        arguments?.let {
            from = if (it.containsKey("from")) {
                it.getString("from")
            } else {
                null
            }
            it.clear()
        }
        if (openShowMore) {
            showPpobCategories()
        }
        if (RemoteConfigUtils.shouldShowBnplFragment())
            showBnplSaldoView()
        changeEmptyStateWordings(isEmptyState)
        if (RemoteConfigUtils.getShowPaymentLandingBottomSheet() && PaymentPrefManager.getInstance().getShowPaymentTutorial()
            && !PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_ADD_BANK_ACCOUNT)) {
            onboardingWidget?.dismiss()
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_NEW_USER_BOTTOMSHEET)
            PaymentTutorialBottomSheet.createInstance(true).show(childFragmentManager, PaymentTutorialBottomSheet.TAG)
            PaymentPrefManager.getInstance().setShowPaymentTutorial(false)
        } else {
            viewModel.checkPpobAndSaldoCoachmark()
        }
        startBannerAutoScrolling()
    }

    override fun onPause() {
        super.onPause()
        viewModel.stopHandlerForEmptyState()
        pagerHandler.removeMessages(0)
        snackbarHandler?.removeCallbacksAndMessages(null)
        snackbarHandler = null
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        viewModel.finishedCoachmark(id)
    }

    /**
     * Callback invoked when onboarding widget is dismissed from the CTA button
     * Doesn't get invoked if dismissed from outside click or close button click.
     * No need to call dismiss on onboardingWidget here, since internally it's already called.
     */
    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        if (id == null) return
        when (id) {
            OnboardingPrefManager.TUTORIAL_PPOB_INTRODUCTION -> {
                showSaldoCoachmark()
            }
            OnboardingPrefManager.TUTORIAL_PEMBAYARAN_CTA -> {
                showPpobCoachmark()
            }
        }
    }

    /**
     * We removed existing runnable when onPause is called
     * When visiting this fragment again, we need to trigger onPageSelected again, so that a new
     * runnable is created and auto scrolling start working.
     */
    private fun startBannerAutoScrolling() {
        if (paymentBannerAdapter.itemCount > 0) {
            binding.vpPaymentBanner.currentItem = 0
        }
    }

    private fun showPaymentOptionsIfWhitelisted() {
        PaymentOptionsBottomSheet.createInstance(AnalyticsConst.BUAT_PEMBAYARAN)
            .show(childFragmentManager, PaymentOptionsBottomSheet.TAG)
    }

    private fun topupSaldoInitiated() {
        val prop = AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN)
            put(AnalyticsConst.WALLET, AnalyticsConst.SALDO)
            put(AnalyticsConst.CURRENT_WALLET_BALANCE, viewModel.viewState.value?.saldoBalance.orNil)
        }
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, prop)
        Utilities.sendEventsToBackendWithBureau(AnalyticsConst.EVENT_WALLET_TOP_UP_CLICK, "wallet_top_up_payment_tab")
        val sourceLink = SourceLink(
            context = requireContext(),
            "${SaldoSignalHandler.saldoLink}?${AnalyticsConst.ENTRY_POINT}=${AnalyticsConst.PAYMENTS}"
        )
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {
                if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_SALDO_IN)) {
                    PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.SALDO)
                } else {
                    startActivity(TopupSaldoActivity.createIntent(requireContext()))
                }
                FirebaseCrashlytics.getInstance().recordException(it)
            }
        )
    }

    override fun tutorialTopupSaldoClicked() {
        topupSaldoInitiated()
    }

    private fun retrieveWeblinkUrl(uri: Uri): String {
        val link = uri.getQueryParameter("link")
        if (link != null && link.startsWith("http")) return link

        val data = uri.getQueryParameter("data")
        if (data != null && data.startsWith("http")) return data

        return ""
    }

    override fun bannerClick(url: String, isKyc: Boolean, status: KycStatus?, bannerTitle: String?, index: Int) {
        if (isKyc && status?.isPending().isFalse) {
            val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN)
                    .put(AnalyticsConst.BUSINESS_CATEGORY, viewModel.getBook()?.bookType)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_KYC_CTA_CLICKED, prop)
        }
        val prop = AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_HOME_PAGE)
                .put(AnalyticsConst.PARAM_INDEX, index)
                .put(AnalyticsConst.PARAM_LANDING_URL, url)
                .put(AnalyticsConst.PARAM_BANNER_TITLE, bannerTitle)
                .put(AnalyticsConst.BANNER_LOCATION, AnalyticsConst.PEMBAYARAN)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_CAROUSEL_BANNER, prop, true, false, false)

        val link = if (url.contains("qris/start")) {
            val uri = Uri.parse(url)
            val extraQueries =
                "entry_point=${AnalyticsConst.QRIS_BANNER}&location=${AnalyticsConst.PEMBAYARAN}"

            val weblink = retrieveWeblinkUrl(uri)
            val extend = if (weblink.isNotBlank()) {
                val weblinkUri = Uri.parse(weblink)
                "&".takeIf { weblinkUri.query.isNotNullOrBlank() } ?: "?"
            } else {
                "&".takeIf { uri.query.isNotNullOrBlank() } ?: "?"
            }
            "$url$extend$extraQueries"
        } else {
            url
        }
        val sourceLink = SourceLink(context = requireContext(), link)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {},
        )
    }

    private fun trackAndStartPpob(fragmentBodyBlock: BodyBlock?) {
        if (fragmentBodyBlock?.is_available.isTrue) {
            if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB)) {
                PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.PPOB)
                return
            }
            val newRedirection = fragmentBodyBlock?.deeplinkAppNeuro.orEmpty()
            val redirection = fragmentBodyBlock?.deeplink_app.orEmpty()
            val deeplink = fragmentBodyBlock?.deeplink_web.orEmpty()
            when{
                newRedirection.isNotBlank() -> {
                    redirect(newRedirection, fragmentBodyBlock?.ppobCategoryName.orEmpty())
                }
                newRedirection.isBlank() && redirection.isNotBlank() -> {
                    redirect(redirection, fragmentBodyBlock?.ppobCategoryName.orEmpty())
                }
                deeplink.isNotBlank() -> {
                    if (Utility.hasInternet()) {
                        startActivity(
                            WebviewActivity.createIntent(
                                requireActivity(), deeplink,
                                fragmentBodyBlock?.display_name
                            )
                        )
                    } else {
                        NoInternetAvailableDialog.show(childFragmentManager)
                    }
                }
            }
            handleAnalytics(fragmentBodyBlock)
        } else {
            PpobUtils.showPpobUnAvailable(requireContext(), fragmentBodyBlock?.ppobCategoryName.orEmpty())
        }
    }

    private fun handleAnalytics(fragmentBodyBlock: BodyBlock?){
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put("ppob_type", fragmentBodyBlock?.analytics_name.orEmpty())
        propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PEMBAYARAN_TAB)
        AppAnalytics.trackEvent(
            AnalyticsConst.EVENT_PPOB_BUY_BUTTON_CLICKED, propBuilder, true, true, false
        )
        SurvicateAnalytics.invokeEventTracker(fragmentBodyBlock?.analytics_name.orEmpty(), requireActivity())
    }

    private fun redirect(redirection: String, category: String) {
        if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_PPOB)) {
            PaymentUtils.showKycKybStatusBottomSheet(parentFragmentManager, AnalyticsConst.PPOB)
            return
        }
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = { redirectWithLegacyLink(category) },
        )
    }

    private fun redirectWithLegacyLink(category: String){
        //in this file we are using neuro only in case of ppob, so we can add conditions here and assign the url if neuro is used for other than ppob.
        val link = "$DEEPLINK_INTERNAL_URL/ppob/product?category=$category"
        val sourceLink = SourceLink(context = requireContext(), link)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = {},
            onFailure = {},
        )
        return
    }

    private fun handleError(errorMessage: String?) {
        ErrorBottomSheet.createInstance(
            errorType = ErrorBottomSheet.Companion.ApiErrorType.CUSTOM,
            message = errorMessage?.ifEmpty { getString(R.string.try_again_or_wait) },
            errorTitle = getString(R.string.server_error_title),
            ctaText = getString(R.string.back)
        ).show(childFragmentManager, ErrorBottomSheet.TAG)
    }

    override fun onPpobSelected(fragmentBodyBlock: BodyBlock?) {
        if (fragmentBodyBlock == null) return
            if (fragmentBodyBlock.coming_soon){
                PpobUtils.showPpobComingSoonDialog(requireContext())
            } else {
                viewModel.checkPpobAvailability(fragmentBodyBlock)
            }
    }

    override fun handleQrisRedirection(qrisResponse: QrisResponse) {
        PaymentUtils.handlePaymentsRedirection(
            requireContext(), childFragmentManager,
            AnalyticsConst.PAYMENT,
            PaymentConst.QRIS_HANDLING_KEY,
            AnalyticsConst.QRIS_BANNER
        )
    }

    override fun showCoachMark(reShowCoachMark: Boolean) {
        if (reShowCoachMark) showPaymentsCoachmark()
        else onboardingWidget?.dismiss(false, false, true)
    }

    override fun openOrderDetail(order: PaymentHistory) {
        if (order.detailService?.equals("EDC_ADAPTER", true).isTrue) {
            //do nothing
        } else {
            val orderType = order.type?.let {
                if (it.equals(PaymentHistory.TYPE_CASHBACK_SETTLEMENT, true))
                    PaymentHistory.TYPE_PAYMENT_OUT
                else it
            }
            Utilities.safeLet(order.orderId, order.type) { orderId, type ->
                startActivity(
                    PaymentHistoryDetailsActivity.createIntent(
                        requireContext(),
                        order.customerId,
                        orderId,
                        orderType,
                        order.displayName,
                        ledgerAccountId = order.ledgerAccountId
                    )
                )
            }
        }
    }

    override fun fetchLinkedOrders(orderId: String, adapterPos: Int) {
        viewModel.fetchLinkedOrders(orderId, adapterPos)
    }

    override fun seeAllOrders() {
        val prop = AppAnalytics.PropBuilder().put(
            AnalyticsConst.ENTRY_POINT2,
            AnalyticsConst.SCROLL_DOWN
        )
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_HISTORY, prop)
        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PAYMENT_PEMBAYARAN_HISTORY, requireActivity())
        startHistoryActivity(SessionManager.getInstance().businessId)
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }

    override fun showSaldoLimits(saldoResponse: SaldoResponse) {
        SaldoLimitsBottomSheet.createInstance(saldoResponse)
            .show(childFragmentManager, SaldoLimitsBottomSheet.TAG)
    }
}
