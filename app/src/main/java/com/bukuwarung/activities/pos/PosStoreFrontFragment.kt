package com.bukuwarung.activities.pos

import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.text.Spannable
import android.text.SpannableStringBuilder
import android.text.style.StyleSpan
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.inventory.detail.EditStockActivity
import com.bukuwarung.activities.pos.adapter.PosProductAdapter
import com.bukuwarung.activities.pos.experiments.PosPaymentWalletFragment
import com.bukuwarung.activities.pos.helpers.IOnBackPressed
import com.bukuwarung.activities.pos.model.PosProduct
import com.bukuwarung.activities.pos.viewmodel.PosClickAction.CLICK
import com.bukuwarung.activities.pos.viewmodel.PosClickAction.EDIT
import com.bukuwarung.activities.pos.viewmodel.PosClickEvent
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.pos.viewmodel.PosViewModelFactory
import com.bukuwarung.activities.productcategory.view.CategoryAction.*
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.databinding.FragmentPosStoreFrontBinding
import com.bukuwarung.inventory.usecases.ProductInventory
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.*
import dagger.android.support.AndroidSupportInjection
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject

class PosStoreFrontFragment : BaseFragment(), IOnBackPressed,
    OnboardingWidget.OnboardingWidgetListener {

    private lateinit var binding: FragmentPosStoreFrontBinding

    @Inject
    lateinit var productInventory: ProductInventory

    @Inject
    lateinit var viewModelFactory: PosViewModelFactory
    private val viewModel: PosViewModel by activityViewModels { viewModelFactory }

    private var searchQuery: String = ""
    private var isSearch: Boolean = false

    // is store front opened for the first time
    private var isFirstOpen = true
    private var isCoachmarkShown = false

    // initial product status when POS is opened
    private var noProducts = true

    // used to trigger event for every new search
    private var isNewSearch: Boolean = true

    // coachmark widget
    private var onBoardingWidget: OnboardingWidget? = null
    private var currentShownCoachmark = COACHMARK_TAP_TO_ADD_PRODUCT

    /**
     * Shared preferences
     */
    private var productCoachmarkShown =
        AppConfigManager.getInstance().posStoreFrontProductCoachmarkShown
    private var editCoachmarkShown = AppConfigManager.getInstance().posStoreFrontEditCoachmarkShown
    private var moveToCartCoachmarkShown =
        AppConfigManager.getInstance().posStoreFrontMoveToCartCoachmarkShown


    /**
     * Remote config
     */
    private var showPosOnBoarding = RemoteConfigUtils.PosExperiments.showPosOnBoarding()
    private var isPosNonCashTaggingEnabled = RemoteConfigUtils.PosExperiments.isNonCashTaggingEnabled()

    private val posProductAdapter: PosProductAdapter by lazy {
        PosProductAdapter(arrayListOf()) {
            when (it.posClickAction) {
                CLICK -> {
                    if (isCoachmarkShown && !editCoachmarkShown) {
                        isCoachmarkShown = false
                        currentShownCoachmark = COACHMARK_EDIT_BOTTOM_SHEET
                        showOnboarding(
                            bodyText = R.string.onboarding_pos_store_front_edit_bottom_sheet,
                            clickedPosition = it.clickedPosition
                        )
                        AppConfigManager.getInstance().posStoreFrontEditCoachmarkShown = true
                        isCoachmarkShown = true
                    }

                    isSearch = searchQuery.isNotEmpty()
                    viewModel.onEventReceived(
                        PosViewModel.Event.OnProductClicked(it)
                    )
                    trackProductAddedToCartEvent(it.product)
                }
                EDIT -> {
                    if (!moveToCartCoachmarkShown) {
                        isCoachmarkShown = false
                        currentShownCoachmark = COACHMARK_MOVE_TO_CART
                        showOnboarding(
                            R.drawable.onboarding_great,
                            bodyText = R.string.onboarding_pos_store_front_move_to_cart
                        )
                        AppConfigManager.getInstance().posStoreFrontMoveToCartCoachmarkShown = true
                        moveToCartCoachmarkShown = true
                        isCoachmarkShown = true
                    }
                    trackProductUpdateDetailsOpenEvent()
                    showCountEditBottomSheet(it)
                }
            }
        }
    }

    private fun showCountEditBottomSheet(posClickEvent: PosClickEvent) {
        Utility.hideKeyboard(requireActivity())

        val bottomSheet = EditPosStockBottomSheetFragment(posClickEvent) {
            it ?: return@EditPosStockBottomSheetFragment
            viewModel.onEventReceived(PosViewModel.Event.OnProductClicked(it))
        }
        bottomSheet.show(parentFragmentManager, "PosStoreFrontBottomSheet")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPosStoreFrontBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        return binding.root
    }

    override fun setupView(view: View) {
        binding.stockUnitRecyclerView.apply {
            adapter = posProductAdapter
            layoutManager = LinearLayoutManager(context)
        }

        binding.closeBtn.setOnClickListener {
            activity?.onBackPressed()
        }

        binding.priceSummaryLayout.setOnClickListener {
            trackPosProceedToCartEvent()
            val fragmentTransaction = activity?.supportFragmentManager?.beginTransaction()
            fragmentTransaction?.let {
                it.replace(R.id.pos_fragment_container, PosCartFragment())
                it.addToBackStack(null)
                it.commit()
            }
        }

        binding.btnSave.setOnClickListener {
            trackPosStoreFrontNextButtonEvent()
            if (binding.btnSave.isEnabled) {
                val fragmentTransaction = activity?.supportFragmentManager?.beginTransaction()
                fragmentTransaction?.let {
                    when {
                        isPosNonCashTaggingEnabled -> {
                            it.replace(R.id.pos_fragment_container, PosPaymentWalletFragment())
                        }
                        else -> {
                            it.replace(R.id.pos_fragment_container, PosPaymentFragment())
                        }
                    }
                    it.addToBackStack(null)
                    it.commit()
                }
            }
        }

        binding.btnAddProduct.setOnClickListener {
            openAddStockScreen()
        }

        binding.emptyScreenLayout.btnAddFirstProduct.setOnClickListener {
            openAddStockScreen()
        }

        setPosModeTooltipVisibleUserProperty()
        setNonCashTaggingEnabledUserProperty()
    }

    private fun openAddStockScreen(isFromPlusIcon: Boolean = false) {
        trackClickAddProductButton(isFromPlusIcon)
        val intent = EditStockActivity.getNewIntent(
            requireActivity(),
            "", User.getBusinessId(),
            isPosAddFlow = true, isFromPlusIcon = isFromPlusIcon, productName = binding.filterView.getSearchQuery()
        )
        startActivityForResult(intent, EditStockActivity.PRODUCT_ADDED_SUCCESS)
    }

    override fun subscribeState() {
        viewModel.productLiveDataMerger.observe(this, object : Observer<List<PosProduct>> {
            override fun onChanged(t: List<PosProduct>?) {}
        })

        viewModel.selectedProduct.observe(this, Observer {
            binding.btnSave.isEnabled = it.isNotEmpty()
            binding.priceSummaryLayout.visibility = it.isNotEmpty().asVisibility()
            binding.tvProductSummaryCount.text = getString(R.string.total_product, it.size)
            binding.tvProductPriceSummary.text =
                Utilities.getLocaleFormattedPrice(viewModel.getSelectedProductTotalPrice())
        })

        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is PosViewModel.State.OnProductLoaded -> {
                    /**
                     * Set noProducts to false if initial product count is non-zero
                     */
                    if (noProducts && it.products.isNotEmpty()) {
                        noProducts = false
                    }
                    if (noProducts && it.products.isEmpty()) {
                        binding.llEmpty.showView()
                        binding.filterView.hideView()
                        binding.nextLinearLayout.hideView()
                        binding.grayLayer.hideView()
                    } else {
                        binding.llEmpty.hideView()
                        binding.filterView.showView()
                        binding.nextLinearLayout.showView()
                        binding.grayLayer.showView()
                    }
                    posProductAdapter.updateData(it.products)
                    if (isFirstOpen) {
                        if (showPosOnBoarding && !productCoachmarkShown) {
                            isCoachmarkShown = true
                            AppConfigManager.getInstance().posStoreFrontProductCoachmarkShown = true
                            showOnboarding()
                        }
                        isFirstOpen = false
                    }
                }
            }
        }

        viewModel.productCategories.observe(this) {
            binding.filterView.setCategories(it)
        }

        binding.filterView.apply {

            setCategoryListener { categoryDataHolder, _ ->
                categoryDataHolder ?: return@setCategoryListener
                when (categoryDataHolder.action) {
                    ADD -> {/*DO NOTHING*/
                    }
                    ALL -> viewModel.onEventReceived(PosViewModel.Event.OnCategoryChanged(null))
                    NORMAL -> viewModel.onEventReceived(PosViewModel.Event.OnCategoryChanged(categoryDataHolder.category?.id))
                }
            }

            setSearchQueryListener(requireActivity()) { query ->
                searchQuery = query.toString()

                binding.btnAddProductGrp.visibility = (searchQuery.isNotEmpty()).asVisibility()
                if (searchQuery.isNotEmpty()) {
                    val spannableStringBuilder = SpannableStringBuilder(
                        "${getString(R.string.add_text)} ${"\""}$searchQuery${"\""} ${
                            getString(R.string.to_item_list)
                        }"
                    )
                    spannableStringBuilder.setSpan(
                        StyleSpan(Typeface.BOLD),
                        7,
                        searchQuery.length + 9,
                        Spannable.SPAN_EXCLUSIVE_EXCLUSIVE
                    )
                    binding.tvSearchProductName.text = spannableStringBuilder
                }

                trackNewProductSearchEvent()
                viewModel.onEventReceived(PosViewModel.Event.OnSearchProducts(query.toString()))
            }

            setSecondaryButton(R.drawable.ic_icon_plus) {
                openAddStockScreen(true)
            }
        }

        binding.filterView.addEventEntryPoint(AnalyticsConst.CASHIER_MODE)
    }

    override fun onBackPressed(): Boolean {
        return true
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == EditStockActivity.PRODUCT_ADDED_SUCCESS && resultCode == AppCompatActivity.RESULT_OK) {
            val productId = data?.getStringExtra("PRODUCT_ID") ?: return
            viewModel.onEventReceived(
                PosViewModel.Event.OnAddNewProduct(productId)
            )
        }
    }

    private fun showOnboarding(
        resDrawable: Int = R.drawable.onboarding_attention,
        bodyText: Int = R.string.onboarding_pos_store_front_tap_to_add_product,
        clickedPosition: Int = 0
    ) {
        lifecycleScope.launch {
            delay(250L)
            binding.stockUnitRecyclerView.post {
                binding.stockUnitRecyclerView.findViewHolderForLayoutPosition(clickedPosition)
                    ?.let { viewHolder ->
                        var anchor = viewHolder.itemView
                        var currentStep = 1
                        var buttonText = ""
                        var currentPref =
                            OnboardingPrefManager.POS_TUTOR_PROCEED_TO_CART_FIRST_TIME_USER
                        when (currentShownCoachmark) {
                            COACHMARK_EDIT_BOTTOM_SHEET -> {
                                anchor = viewHolder.itemView.findViewById(R.id.ll_product_count)
                                currentStep = 2
                                currentPref =
                                    OnboardingPrefManager.POS_TUTOR_PRODUCT_EDIT_FIRST_TIME_USER
                            }
                            COACHMARK_MOVE_TO_CART -> {
                                anchor = binding.btnSave
                                currentStep = 3
                                buttonText = getString(R.string.ok)
                                currentPref =
                                    OnboardingPrefManager.POS_TUTOR_PRODUCT_TO_CART_FIRST_TIME_USER
                            }
                        }
                        onBoardingWidget = OnboardingWidget.createInstance(
                            requireActivity(),
                            this@PosStoreFrontFragment,
                            currentPref,
                            anchor,
                            resDrawable,
                            "",
                            getString(bodyText),
                            buttonText,
                            FocusGravity.CENTER,
                            ShapeType.RECTANGLE_FULL,
                            currentStep,
                            3,
                            sendAnalytics = true,
                            sendAnalyticsOnDismiss = true,
                            delay = 0,
                            isPerformClick = true
                        )
                    }
            }
        }
    }

    /**
     * Events
     */

    private fun trackProductAddedToCartEvent(posProduct: PosProduct) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.PRODUCT_NAME, posProduct.product.name)
            .put(AnalyticsConst.PRODUCT_QTY, posProduct.count)
            .put(
                AnalyticsConst.QUANTITY_TYPE,
                Utility.getQuantityTypeFromTotalStock(posProduct.count)
            )

        // if item is newly added to cart, trigger event to track unique SKUs
        if (posProduct.count == 1.0) {
            AppAnalytics.trackEvent(AnalyticsConst.POS_PRODUCT_ADDED_TO_CART, propBuilder)
        }
    }

    private fun trackProductUpdateDetailsOpenEvent() {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.PRODUCT_NAME, AnalyticsConst.STOREFRONT)
        AppAnalytics.trackEvent(AnalyticsConst.POS_UPDATE_PRODUCT_DETAILS_OPEN, propBuilder)
    }

    private fun trackNewProductSearchEvent() {
        if (isNewSearch && searchQuery.isNotEmpty()) {
            // track new search
            AppAnalytics.trackEvent(AnalyticsConst.POS_SEARCH_BAR_USED)
            isNewSearch = false
        } else if (searchQuery.isEmpty()) {
            isNewSearch = true
        }
    }

    private fun trackPosStoreFrontNextButtonEvent() {
        val propBuilder = getCartSummaryPropBuilder()
        propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.POS_STOREFRONT)
        AppAnalytics.trackEvent(AnalyticsConst.POS_STOREFRONT_NEXT_BUTTON, propBuilder)
    }

    private fun trackPosProceedToCartEvent() {
        val propBuilder = getCartSummaryPropBuilder()
        AppAnalytics.trackEvent(AnalyticsConst.POS_PROCEED_TO_CART, propBuilder)
    }

    private fun getCartSummaryPropBuilder(): AppAnalytics.PropBuilder {
        val totalProductQty = viewModel.getSelectedProductTotalQty()
        val uniqueProductCount = viewModel.getSelectedUniqueProductCount()
        val totalAmountToPay = viewModel.getSelectedProductTotalPrice()

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.TOTAL_AMOUNT, totalAmountToPay)
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)
            .put(AnalyticsConst.TOTAL_PRODUCT_QTY, totalProductQty)

        return propBuilder
    }

    private fun trackClickAddProductButton(isFromPlusIcon: Boolean) {
        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder.put(
            AnalyticsConst.ENTRY_POINT2,
            if (isFromPlusIcon) AnalyticsConst.POS_PLUS_ICON else AnalyticsConst.POS_SHORTCUT
        )

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_INVENTORY_ADD_PRODUCT, propBuilder)
    }

    /**
     * Set user properties for POS
     */

    private fun setPosModeTooltipVisibleUserProperty() {

    }

    private fun setNonCashTaggingEnabledUserProperty() {

    }

    companion object {
        fun getInstance() = PosStoreFrontFragment()
        var price: Double = 0.0

        const val COACHMARK_TAP_TO_ADD_PRODUCT = "tap_to_add_product"
        const val COACHMARK_EDIT_BOTTOM_SHEET = "edit_bottom_sheet"
        const val COACHMARK_MOVE_TO_CART = "move_to_cart"
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
        when (currentShownCoachmark) {
            COACHMARK_MOVE_TO_CART -> {
                isCoachmarkShown = false
            }
            COACHMARK_EDIT_BOTTOM_SHEET -> {
                isCoachmarkShown = false
            }
        }
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        // do nothing
    }
}