package com.bukuwarung.activities.pos.experiments

import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.activityViewModels
import com.bukuwarung.R
import com.bukuwarung.activities.expense.detail.CashTransactionDetailActivity
import com.bukuwarung.activities.pos.PosActivity
import com.bukuwarung.activities.pos.experiments.customviews.PosTabLayoutBadgeCustomView
import com.bukuwarung.activities.pos.viewmodel.PosViewModel
import com.bukuwarung.activities.pos.viewmodel.PosViewModelFactory
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.FragmentPosPaymentWalletBinding
import com.bukuwarung.utils.*
import com.google.android.material.tabs.TabLayout
import com.google.android.material.tabs.TabLayoutMediator
import dagger.android.support.AndroidSupportInjection
import javax.inject.Inject

class PosPaymentWalletFragment : BaseFragment() {
    private lateinit var binding: FragmentPosPaymentWalletBinding

    @Inject
    lateinit var viewModelFactory: PosViewModelFactory
    private val viewModel: PosViewModel by activityViewModels { viewModelFactory }

    private var totalAmountToPay: Double = 0.0
    private var totalBuyingPrice: Double = 0.0
    private var inputtedAmount: Double = 0.0
    private var change: Double = 0.0

    private var posTabLayoutBadgeCustomView: PosTabLayoutBadgeCustomView? = null

    private lateinit var posPaymentViewPagerAdapter: PosPaymentViewPagerAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = FragmentPosPaymentWalletBinding.inflate(inflater, container, false)
        AndroidSupportInjection.inject(this)
        return binding.root
    }

    override fun setupView(view: View) {
        totalAmountToPay = viewModel.getSelectedProductTotalPrice()
        totalBuyingPrice = viewModel.getSelectedProductTotalBuyingPrice()

        posPaymentViewPagerAdapter = PosPaymentViewPagerAdapter(this)
        binding.vpPayment.adapter = posPaymentViewPagerAdapter
        TabLayoutMediator(binding.tlPayment, binding.vpPayment) { tab, position ->
            when (position) {
                0 -> {
                    tab.text = context?.getString(R.string.pos_cash_tunai)
                }
                else -> {
                    posTabLayoutBadgeCustomView = PosTabLayoutBadgeCustomView(requireActivity())
                    posTabLayoutBadgeCustomView?.hideNewBadgeIfShown()
                    tab.customView = posTabLayoutBadgeCustomView
                }
            }
        }.attach()

        binding.tlPayment.addOnTabSelectedListener(object: TabLayout.OnTabSelectedListener {
            override fun onTabSelected(tab: TabLayout.Tab?) {
                tab?.let {
                    when (it.position) {
                        1 -> {
                            posTabLayoutBadgeCustomView?.setSelectedColor()
                        }
                        else -> {
                            posTabLayoutBadgeCustomView?.setUnselectedColor()
                        }
                    }
                }
            }

            override fun onTabUnselected(tab: TabLayout.Tab?) {
                // No-op
            }

            override fun onTabReselected(tab: TabLayout.Tab?) {
                // No-op
            }
        })


        binding.closeBtn.setOnClickListener {
            activity?.onBackPressed()
        }

        val totalPriceStr = "Rp${Utility.formatCurrency(totalAmountToPay)}"
        binding.tvTotalAmount.text = totalPriceStr
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is PosViewModel.State.OnTransactionSaved -> {
                    binding.lavSuccess.showForOnce(binding.successView, 75) {
                        it.trxId?.let { trxId ->
                            trackPosPaymentCompleteEvent(
                                trxId = trxId,
                                uniqueProductCount = it.uniqueProductCount,
                                totalProductQty = it.totalProductQty
                            )
                        }

                        val detailIntent = Intent(
                            requireContext(),
                            CashTransactionDetailActivity::class.java
                        ).apply {
                            putExtra(CashTransactionDetailActivity.TRX_ID_PARAM, it.trxId)
                            putExtra(CashTransactionDetailActivity.IS_EXPENSE_PARAM, false)
                            putExtra("isRedirectedFromCashTransaction", true)
                            putExtra(
                                CashTransactionDetailActivity.IS_REDIRECTED_FROM_POS_MODE,
                                true
                            )
                            putExtra("changeAmount", change)
                        }

                        val posTrxCount =
                            TransactionRepository.getInstance(activity).posTransactionCountWithDeletedRecords
                        /**
                         * track first pos transaction
                         */

                        startActivity(detailIntent)
                        requireActivity().finish()
                    }
                }
            }
        }
    }

    private fun trackPosPaymentCompleteEvent(
        trxId: String,
        uniqueProductCount: Int,
        totalProductQty: Double
    ) {
        inputtedAmount = viewModel.userInputtedCustomAmount
        var changeAmount = inputtedAmount - totalAmountToPay
        var exactAmount = calculateExactAmountType(changeAmount)

        // if user presses btnReceiveMoney, changeAmount and inputtedAmount should be 0
        if (viewModel.trxType == EXACT_AMOUNT) {
            changeAmount = 0.0
            exactAmount = AnalyticsConst.EXACT_AMOUNT
            inputtedAmount = 0.0
        }

        var modeOfPayment = AnalyticsConst.CASH

        if (viewModel.getSelectedNonCashPaymentMethod() != PosViewModel.CASH_PAYMENT_METHOD) {
            modeOfPayment = AnalyticsConst.NON_CASH
        }

        val propBuilder = AppAnalytics.PropBuilder()
        propBuilder
            .put(AnalyticsConst.MODE_OF_PAYMENT, modeOfPayment)
            .put(AnalyticsConst.EXACT_AMOUNT, exactAmount)
            .put(AnalyticsConst.TRANSACTION_ID, trxId)
            .put(AnalyticsConst.TOTAL_AMOUNT, totalAmountToPay)
            .put(AnalyticsConst.CUSTOM_AMOUNT, inputtedAmount)
            .put(AnalyticsConst.CHANGE_AMOUNT, changeAmount)
            .put(AnalyticsConst.UNIQUE_PRODUCT_COUNT, uniqueProductCount)
            .put(AnalyticsConst.TOTAL_PRODUCT_QTY, totalProductQty)
            .put(AnalyticsConst.ENTRY_POINT2, viewModel.posPaymentCompleteEntryPoint)
            .put(
                AnalyticsConst.SUM_PRODUCT_BUYING_PRICE,
                viewModel.getSelectedProductTotalBuyingPrice()
            )
            .put(AnalyticsConst.NOTA_STANDARD_ENABLED, RemoteConfigUtils.shouldShowNewPosInvoice())

        if (viewModel.getSelectedNonCashPaymentMethod() != PosViewModel.CASH_PAYMENT_METHOD) {
            propBuilder
                .put(AnalyticsConst.NON_CASH_CHANNEL, viewModel.getSelectedNonCashPaymentMethod())
        }

        if (PosActivity.from.isNotNullOrEmpty()) {
            propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE)
        }

        AppAnalytics.trackEvent(AnalyticsConst.POS_PAYMENT_COMPLETE, propBuilder)
    }

    private fun calculateExactAmountType(changeAmount: Double): String {
        return when {
            changeAmount < 0.0 -> {
                AnalyticsConst.LESS
            }
            changeAmount == 0.0 -> {
                AnalyticsConst.EXACT_AMOUNT
            }
            else -> {
                AnalyticsConst.MORE
            }
        }
    }

    companion object {
        const val EXACT_AMOUNT = "exact_amount"

        fun getInstance() = PosPaymentWalletFragment()
    }
}