package com.bukuwarung.activities.profile

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.graphics.Typeface
import android.net.Uri
import android.os.Build
import android.os.Build.VERSION
import android.os.Bundle
import android.os.Environment
import android.os.Handler
import android.os.SystemClock
import android.provider.MediaStore
import android.text.SpannableStringBuilder
import android.util.DisplayMetrics
import android.util.TypedValue
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import android.widget.TextView
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.text.bold
import androidx.lifecycle.Observer
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.viewpager2.widget.ViewPager2
import com.bukuwarung.Application
import com.bukuwarung.BuildConfig
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.card.BusinessCardActivity
import com.bukuwarung.activities.card.BusinessCardShareActivity
import com.bukuwarung.activities.card.newcard.BusinessCardDesign
import com.bukuwarung.activities.card.newcard.BusinessCardViewModel
import com.bukuwarung.activities.card.newcard.NewBusinessCardActivity
import com.bukuwarung.activities.card.preview.LayoutToImageHandler
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.homepage.data.RefreeEntryPointData
import com.bukuwarung.activities.invoice.InvoiceSettingActivity
import com.bukuwarung.activities.notification.NotificationActivity
import com.bukuwarung.activities.notification.discover.InfoDiscoveryAdapter
import com.bukuwarung.activities.print.adapter.PrinterPrefManager
import com.bukuwarung.activities.print.setup.SetupPrinterActivity
import com.bukuwarung.activities.profile.adapter.ItemMarginDecorator
import com.bukuwarung.activities.profile.adapter.LainnyaProgressBarAdapter
import com.bukuwarung.activities.profile.adapter.ProfileBannerAdapter
import com.bukuwarung.activities.profile.businessprofile.BusinessProfileWebviewActivity
import com.bukuwarung.activities.profile.businessprofile.NgBusinessProfileActivity
import com.bukuwarung.activities.profile.model.ProgressBarItemLiannya
import com.bukuwarung.activities.profile.update.dialogs.UserProfileActivity
import com.bukuwarung.activities.referral.ShareApp
import com.bukuwarung.activities.referral.leaderboard.LeaderboardWebviewActivity
import com.bukuwarung.activities.referral.leaderboard.LeaderboardWebviewActivity.Companion.createIntent
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyAccount
import com.bukuwarung.activities.referral.leaderboard.models.LoyaltyTier
import com.bukuwarung.activities.referral.payment_referral.ReferralActivity
import com.bukuwarung.activities.selfreminder.SelfReminderActivity
import com.bukuwarung.activities.selfreminder.SetSelfReminderActivity
import com.bukuwarung.activities.settings.SettingsActivity
import com.bukuwarung.activities.stickers.StickerMainActivity
import com.bukuwarung.activities.successmessage.TutorialCompletionDialog
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.activities.superclasses.DataHolder
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.AppAnalytics.PropBuilder
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.DEEPLINK_INTERNAL_URL
import com.bukuwarung.constants.AppConst.DEEPLINK_SCHEME_BUKUWARUNG
import com.bukuwarung.constants.AppConst.INT_ZERO
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.constants.RemoteConfigConst
import com.bukuwarung.data.restclient.CompletionRequest
import com.bukuwarung.database.entity.AppNotification
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.databinding.TabLayoutBusinessProfileBinding
import com.bukuwarung.dialogs.HelpDialog
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.dialogs.stocktab.StockTabWarningPopup
import com.bukuwarung.model.BannerInfo
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Neuro
import com.bukuwarung.neuro.api.SourceLink
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.ReferralDataResponse
import com.bukuwarung.payments.data.model.SaldoResponse
import com.bukuwarung.payments.deeplink.handler.PaymentCheckoutSignalHandler
import com.bukuwarung.payments.history.OrderHistoryActivity
import com.bukuwarung.payments.ppob.PpobUtils
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.OnboardingPrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.tutor.shape.FocusGravity
import com.bukuwarung.tutor.shape.ShapeType
import com.bukuwarung.tutor.view.OnboardingWidget
import com.bukuwarung.utils.ImageUtils
import com.bukuwarung.utils.NotificationUtils
import com.bukuwarung.utils.PermissonUtil
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.ShareUtils
import com.bukuwarung.utils.TooltipBuilder
import com.bukuwarung.utils.TooltipBuilder.Companion.builder
import com.bukuwarung.utils.Utilities.launchPlayStoreWithUri
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.asVisibility
import com.bukuwarung.utils.getScreenWidth
import com.bukuwarung.utils.hideView
import com.bukuwarung.utils.isFeatureSupported
import com.bukuwarung.utils.isNotNullOrEmpty
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orNil
import com.bukuwarung.utils.recordException
import com.bukuwarung.utils.returnObject
import com.bukuwarung.utils.setDrawable
import com.bukuwarung.utils.setStyleButtonFill
import com.bukuwarung.utils.setStyleButtonOutline
import com.bukuwarung.utils.showView
import com.bukuwarung.utils.singleClick
import com.bukuwarung.utils.subscribeSingleLiveEvent
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import com.google.android.gms.tasks.TaskExecutors
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.tabs.TabLayoutMediator
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.google.gson.Gson
import com.google.gson.GsonBuilder
import com.google.gson.JsonSyntaxException
import com.google.gson.reflect.TypeToken
import io.github.douglasjunior.androidSimpleTooltip.SimpleTooltip
import java.io.File
import javax.inject.Inject

class ProfileTabFragment : BaseFragment(), OnboardingWidget.OnboardingWidgetListener, Navigator {
    interface ProfileTabFragmentListener {
        fun callLoginFromProfileTab(entryPoint: String)
        fun handleNormalBackPressedFromProfile()
        fun redirectToPaymentTab()
    }

    companion object {
        const val USER_PROFILE_TEMP: String = "user_profile_temp"
        const val SHOW_EDIT_PROFILE = "show_edit_profile"
        const val USER_PROFILE = "user_profile"
        const val USER_BUSINESS_PROFILE = "user_business_profile"
        const val USER_KYC_COMPLETE = "user_kyc_complete"
        const val BANK_ACCOUNT = "bank_account"
        const val BRICK_ACCOUNT = "brick_account"
        val FROM_MISSION: String = "FROM_MISSION"
    }

    private var savedBizzCardDesign: BusinessCardDesign? = null
    private var bookEntity: BookEntity? = null
    private var userProfileTemp: UserProfileEntity? = null
    private var reminderIsEmpty: Boolean = false
    private var tooltip: SimpleTooltip? = null
    private lateinit var binding: TabLayoutBusinessProfileBinding
    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null
    private var hasDrawBlur = false
    private var listener: ProfileTabFragmentListener? = null
    private var bookId: String = ""
    private var onboardingWidget: OnboardingWidget? = null
    private var isListBankCoachmarkShown = false
    private var isAutoRecordCoachmarkShown = false
    private var firstTimeOnResume = true
    private var showStreakBanner = false
    private var lastButtonSaveClicked: Long = 0
    lateinit var profilePinAdapter: ProfilePinAdapter
    var showLainnyaMenuWithWidget = false
    private var basicSectionStatus: String? = null
    private var operationalSectionStatus: String? = null
    private var additionalSectionStatus: String? = null
    var haveBankAccount = false
    var lastTime: Long = 0
    val handler = Handler()
    private val profileBannerAdapter: ProfileBannerAdapter by lazy { ProfileBannerAdapter(this::profileBannerClick) }
    private val pagerhandler by lazy { Handler() }
    private lateinit var progressBarBlock: List<ProgressBarItemLiannya>
    private var isAllSectionsCompleted = false
    private var visibleItemsList = mutableListOf<ProgressBarItemLiannya>()
    private var progressBarAdapter: LainnyaProgressBarAdapter? = null
    private var businessRepository: BusinessRepository? = null
    private val delay : Long = 1000


    @Inject
    lateinit var viewModel: ProfileTabViewModel

    @Inject
    lateinit var businessCardViewModel: BusinessCardViewModel

    @Inject
    lateinit var neuro: Neuro

    override fun onCreate(bundle: Bundle?) {
        super.onCreate(bundle)
        setHasOptionsMenu(false)
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is ProfileTabFragmentListener) {
            listener = context
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View? {
        binding = TabLayoutBusinessProfileBinding.inflate(layoutInflater, viewGroup, false)
        viewModel.onEventReceived(ProfileTabViewModel.Event.GetPinsForProfile)

        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PROFILE_VISIT, false, true, false);

        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PROFILE_VISIT, requireActivity())
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.vpProfileBanner.registerOnPageChangeCallback(object :
            ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)

                val runnable = Runnable {
                    binding.vpProfileBanner.currentItem =
                        (position + 1) % profileBannerAdapter.itemCount
                }
                pagerhandler.postDelayed(
                    runnable,
                    RemoteConfigUtils.getProfileBannerAutoScrollTime()
                )
            }

            override fun onPageScrollStateChanged(state: Int) {
                super.onPageScrollStateChanged(state)
                /**
                 * The user swiped forward or back and we need to
                 * invalidate the previous handler.
                 */
                if (state == ViewPager2.SCROLL_STATE_DRAGGING) {
                    pagerhandler.removeMessages(0)

                }
            }

            override fun onPageScrolled(
                position: Int,
                positionOffset: Float,
                positionOffsetPixels: Int
            ) {
            }
        })
        binding.vpProfileBanner.adapter = profileBannerAdapter
        binding.vpProfileBanner.apply {
            layoutParams.height = ((activity?.let { getScreenWidth(it) } ?: 0) * 0.22).toInt()
        }
        TabLayoutMediator(binding.tbProfileBanner, binding.vpProfileBanner) { tab, position ->
        }.attach()

        changeAppUpdateBadgeVisibility()
        // showLainnyaCarouselItemBanner()
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is ProfileTabViewModel.State.ShowIncentiveLayout -> showIncentiveLayout(it.showLayout)
                is ProfileTabViewModel.State.ShowTokokoDownloadLayout -> showTokokoDownloadLayout(it.show)
                is ProfileTabViewModel.State.ShowPaymentReferral -> showPaymentReferral(it.show)
                is ProfileTabViewModel.State.ShowBukuPay -> handleBukuPayActive(it.show)
                is ProfileTabViewModel.State.SetProfileData -> initProfileView(
                    it.trxCount,
                    it.bookEntity,
                    it.hasPendingPayment,
                    it.phoneNumber,
                    it.hasEditCard,
                    it.showPpobBanner
                )
                is ProfileTabViewModel.State.ContinueUploadImage -> setProfilePic(it.uri.toString())
                is ProfileTabViewModel.State.ContinueOnUpdateClicked -> handleUpdateProcess(it.useFlexibleUpdate)
                is ProfileTabViewModel.State.ReturnCardBackground -> context?.run {
                    binding.businessCardPreview.businessCardCanvas.background =
                        ContextCompat.getDrawable(this, it.res)
                }
                is ProfileTabViewModel.State.HandlePaymentVisibility -> handlePaymentVisibility(
                    it.hasBankAccount,
                    it.paymentTabEnabled,
                    it.hasTransaction
                )
                is ProfileTabViewModel.State.ShouldShowBankListCoachmark -> showBankListCoachmark(it.hasShownListBankCoachmark)
                is ProfileTabViewModel.State.SetSummaryData -> {

                    if (it.summaryResponse.count?.paymentOut.orNil > 0) {
                        binding.paymentInfoMessage.moneySavedMessage.text =
                            SpannableStringBuilder(getString(R.string.money_saved_message)).bold {
                                append(Utility.formatAmount(AppConst.PAYMENT_FEE * it.summaryResponse.count?.paymentOut.orNil))
                                    .append("!")
                            }
                    }
                    binding.buttonHistory.visibility =
                        if (it.summaryResponse.countAll ?: INT_ZERO > 0) View.VISIBLE else View.GONE
                }
                is ProfileTabViewModel.State.GoToRegisterBankAccount -> goToRegisterBankAccount()
                is ProfileTabViewModel.State.StockTabInitialVisibility -> {
                    binding.stockFeature.isChecked = it.visibility
                    changeStockTabText(it.visibility)
                }
                is ProfileTabViewModel.State.AutoRecordTabInitialVisibility -> {
                    binding.autoRecordTxnFeature.isChecked = it.visibility
                }
                ProfileTabViewModel.State.ShowStockTabWarningPopup -> {
                    activity?.also { activity ->
                        StockTabWarningPopup(activity, {
                            viewModel.onEventReceived(
                                ProfileTabViewModel.Event.OnStockTabWarningPopUpSelected(
                                    false
                                )
                            )
                        },
                            {
                                viewModel.onEventReceived(
                                    ProfileTabViewModel.Event.OnStockTabWarningPopUpSelected(
                                        true
                                    )
                                )
                            }).show()
                    }
                }
                is ProfileTabViewModel.State.ShowBanner -> {
                    val bannerUrlObjectString: String =
                        if (it.showLendingBanner) RemoteConfigUtils.getProfileTabLendingBannerUrl() else RemoteConfigUtils.getProfileTabNormalBannerUrl()
                    val gson: Gson = GsonBuilder().create()
                    val jsonType = object : TypeToken<List<BannerInfo?>?>() {}.type
                    var bannerInfoList: List<BannerInfo> = gson.fromJson(bannerUrlObjectString, jsonType)
                    bannerInfoList = bannerInfoList.filter { list -> list.bannerUrl != null && isFeatureSupported(list.appVersion) }
                    if (bannerInfoList.isNotEmpty() && !RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()) {
                        binding.vpProfileBanner.visibility = View.VISIBLE
                        if (bannerInfoList.size > 1) {
                            binding.tbProfileBanner.visibility = View.VISIBLE
                        }
                        profileBannerAdapter.setItem(bannerInfoList)
//                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_PPOB_BANNER, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
//                        viewModel.onEventReceived(ProfileTabViewModel.Event.ActivatePpob)
//                        (activity as MainActivity).enablePaymentsTab()
//                        listener?.redirectToPaymentTab()
                        //  }
                    }
                    else -> {}
                }
                is ProfileTabViewModel.State.ChangeStockSwitchStatus -> {
                    binding.stockFeature.isChecked = it.status
                    changeStockTabText(it.status)
                    MainActivity.startActivityAndClearTop(activity)
                }

                is ProfileTabViewModel.State.SetProfilePins -> {
                    showProfilePins(it.profilePins)
                }

                is ProfileTabViewModel.State.SetPinUnlocked -> {
                    binding.inProfilePins.ivPinLock.visibility = View.GONE
                    profilePinAdapter.setLockstateForPins(it.count)
                }

                is ProfileTabViewModel.State.SetFloatingButtonVisibility -> {
                    setReferralFloatingButtonVisibility(it.count)
                }

                is ProfileTabViewModel.State.SetFloatingButtonHide -> {
                    if (it.isVisible) {
                        handler.postDelayed(Runnable {
                            binding.clUndangFloatEntry.animate().translationX(130f).setDuration(100)
                                .start()
                            binding.tvUndangFloatEntry.visibility = View.VISIBLE
                            binding.vwUndangFloatEntry.visibility = View.GONE
                        }, 5000)
                    } else {
                        handler.removeCallbacksAndMessages(null)
                    }
                }
                is ProfileTabViewModel.State.OnProfileTierLoaded -> {
                    binding.loyaltyWidget.apply {
                        setPoint(it.loyaltyAccount)
                        setTier(it.tier)
                        visibility = it.isWhitelister.asVisibility()
                        AppAnalytics.setUserProperty("current_quarter_tier", it.tier.tierName)
//                        AppAnalytics.setUserProperty("loyalty_points_earned", it.loyaltyAccount.activePoints.toString())
                    }
                }

                is ProfileTabViewModel.State.OnSaldoBonusLoaded -> {
                    handleSaldoBonusData(it.saldoBonusResponse,it.loyaltyAccount, it.tier, it.isWhitelister)
                }

                is ProfileTabViewModel.State.OnLoyaltySectionVisibility -> {
                    binding.loyaltyWidget.apply {
                        visibility = it.isVisible.asVisibility()
                    }
                }
                is ProfileTabViewModel.State.OnReferralDataLoaded -> {
                    handleReferralData(it.referralDataResponse)
                }
            }
        }


        this.viewModel.getDataHolderList().observe(viewLifecycleOwner,
            Observer<List<DataHolder?>?> { list -> // Different Activites are shown based on reminders are present or not
                reminderIsEmpty = list == null || list.isEmpty() || list.size <= 1

            })
    }

    private fun handleReferralData(referralDataResponse: ReferralDataResponse) {
        with(binding.loyaltyWidgetWithSaldoBonus) {
            updateReferralData(referralDataResponse)
        }
    }

    private fun handleSaldoBonusData(saldoBonusResponse: SaldoResponse, loyaltyAccount: LoyaltyAccount, tier: LoyaltyTier, isWhiteListed: Boolean) {
        binding.loyaltyWidgetWithSaldoBonus.apply {
            setPoint(saldoBonusResponse)
            if (RemoteConfigUtils.getLoyaltyWidgetType() == 0 || RemoteConfigUtils.getLoyaltyWidgetType() == 1 ||
                (RemoteConfigUtils.getLoyaltyWidgetType() == 2 && !RemoteConfigUtils.showReferralEntryPointHome())
            ) {
                setTier(tier)
            }
//            visibility = isWhiteListed.asVisibility()
//            AppAnalytics.setUserProperty("loyalty_enable", isWhiteListed.toString())
//            AppAnalytics.setUserProperty("current_quarter_tier", tier.tierName)
//            AppAnalytics.setUserProperty("loyalty_points_earned", loyaltyAccount.activePoints.toString())
        }
    }

    private fun showLainnyaCarouselItemBanner() {
        if (OnboardingPrefManager.getInstance().getHasShownLainnyaCarouselItems()) {
            // banner logic to be added here
            OnboardingPrefManager.getInstance().setHasShownLainnyaCarouselItems()
        }
    }

    private fun showProfilePins(pins: List<ProfilePins>) {
        binding.inProfilePins.ivPinLock.setOnClickListener {
            context?.also {
                val tooltipBuilder = TooltipBuilder.builder(it)
                    .setAnchor(binding.inProfilePins.ivPinLock)
                    .setText("Buat 1 catatan transaksi atau utang untuk membuka fitur ini.")
                    .setGravity(Gravity.END)
                tooltip = tooltipBuilder.build()
                tooltip?.show()
            }
        }

        val txnCount = TransactionRepository.getInstance(requireContext()).transactionCount + FeaturePrefManager.getInstance().paymentTransactionCount

        if (txnCount > 0) {
            binding.inProfilePins.ivPinLock.visibility = View.GONE
        }

        profilePinAdapter = ProfilePinAdapter(pins, txnCount) {
            resolveDestination(pins, it)
        }
        binding.inProfilePins.rvProfilePin.apply {
            layoutManager = GridLayoutManager(requireContext(), getColumnCount())
            adapter = profilePinAdapter
        }
    }


    // This would be required to get number of rows in grid according to screen desity and width
    fun getNumberOfRows(): Int {
        val displayMetrics: DisplayMetrics = requireContext().resources.displayMetrics
        val dpWidth: Float = displayMetrics.widthPixels / displayMetrics.density
        return (dpWidth / 100).toInt()
    }

    fun getColumnCount(): Int {
        return RemoteConfigUtils.getProfilePinColumnCount().toInt()
    }

    private fun changeStockTabText(status: Boolean) {
        if (status) {
            binding.stockLayout.text = getString(R.string.stock_tab_switch_label_active)
        } else {
            binding.stockLayout.text = getString(R.string.stock_tab_switch_label_inactive)
        }
    }

    private fun handlePaymentVisibility(
        hasBankAccount: Boolean,
        paymentTabEnabled: Boolean,
        hasTransaction: Boolean
    ) {
        haveBankAccount = hasBankAccount
        binding.gradientBgView.visibility = View.VISIBLE
        binding.divider.visibility = View.VISIBLE
        if (paymentTabEnabled) {
            binding.txtLabelDigitalPayment.visibility = View.GONE
            binding.safeAnimView.visibility = View.GONE
            binding.paymentLayout.visibility = View.GONE
            binding.paymentInfoMessage.rootLayout.visibility = View.GONE
            if (haveBankAccount) {
                binding.registerBankLayout2.visibility = View.GONE
                binding.gradientBgView.visibility = View.GONE
                binding.divider.visibility = View.GONE
            } else {
                if (!RemoteConfigUtils.NewHomePage.shouldShowNewHomePage())
                    binding.registerBankLayout2.visibility = View.VISIBLE
            }
        } else {
            binding.txtLabelDigitalPayment.visibility = View.VISIBLE
            binding.safeAnimView.visibility = View.VISIBLE
            binding.paymentLayout.visibility = View.VISIBLE
            binding.paymentInfoMessage.rootLayout.visibility = View.VISIBLE
            binding.registerBankLayout2.visibility = View.GONE
            if (!RemoteConfigUtils.NewHomePage.shouldShowNewHomePage())
                binding.registerBankLayout.visibility =
                    if (hasBankAccount || !hasTransaction) View.GONE else View.VISIBLE
        }
        if (haveBankAccount && binding.settingsSubmenu.visibility == View.VISIBLE) {
            if (!firstTimeOnResume)
                viewModel.onEventReceived(ProfileTabViewModel.Event.CheckForCoachmark)
        } else {
            binding.listBankGroup.visibility = View.GONE
        }
    }

    private fun handleBukuPayActive(show: Boolean) {
        if (show) {
            binding.txtLabelDigitalPayment.setCompoundDrawables(
                resources.getDrawable(R.drawable.logo_buku_pay, null),
                null, null, null
            )
            binding.txtLabelDigitalPayment.text =
                getString(R.string.payment_card_layout_label_digital_payment)
        } else {
            binding.txtLabelDigitalPayment.setCompoundDrawables(null, null, null, null)
            binding.txtLabelDigitalPayment.text =
                getString(R.string.payment_card_layout_label_digital_payment_2)
        }
    }

    private fun showIncentiveLayout(showLayout: Boolean) {
//        if(showLayout){
//            binding.incentiveLayout.rootLayout.showView()
//        }else{
//            binding.incentiveLayout.rootLayout.hideView()
//        }
    }

    private fun showTokokoDownloadLayout(show: Boolean) {
        binding.tokokoLabel.visibility = View.VISIBLE
        binding.tokokoBtn.visibility = View.VISIBLE
        binding.tokokoDivider.visibility = View.VISIBLE
    }

    private fun showPaymentReferral(show: Boolean) {
        binding.referralBtn.visibility = View.GONE
        binding.ReferralLabel.visibility = View.GONE
        binding.referralDivider.visibility = View.GONE
    }

    private fun setReferralFloatingButtonVisibility(count: Int) {
        val isReferralFloatingButtonEnabled = RemoteConfigUtils.ReferralFloatingFeature.isFloatingButtonEnabled()
        val referralFloatingButtonVisibilityThreshold = RemoteConfigUtils.ReferralFloatingFeature.floatingButtonVisibilityThreshold()

        binding.clUndangFloatEntry.visibility = (isReferralFloatingButtonEnabled &&
                count > referralFloatingButtonVisibilityThreshold).asVisibility()
    }

    override fun setupView(view: View) {
        viewModel.onEventReceived(ProfileTabViewModel.Event.OnCreateView)
        viewModel.isLendingEligible()
        context?.let {
            val itemDecoration = ItemMarginDecorator(
                it.resources.getDimension(R.dimen._16dp).toInt(),
                it.resources.getDimension(R.dimen._12dp).toInt()
            )
            binding.incentiveLayout.rvProgressBar.addItemDecoration(itemDecoration)
            binding.incentiveLayout.rvProgressBar.layoutManager = LinearLayoutManager(
                it,
                LinearLayoutManager.HORIZONTAL, false
            )
        }

         if(RemoteConfigUtils.shouldShowSaldoBonus()){
            binding.loyaltyWidgetWithSaldoBonus.visibility = View.VISIBLE
             binding.loyaltyWidget.visibility = View.GONE

             binding.loyaltyWidgetWithSaldoBonus.setTierClickListener {
                 AppAnalytics.trackEvent("saldo_button_click", PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
                 val webViewIntent = WebviewActivity.createIntent(
                     activity,
                     RemoteConfigUtils.getLoyaltyMembershipUrl(), "Integrasi Institusi Keuangan", false, "loyalty_account", "lainnya"
                 )
                 startActivity(webViewIntent)
             }

             binding.loyaltyWidgetWithSaldoBonus.setOnPointClickListener {
                 AppAnalytics.trackEvent("redeem_screen_open", PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
                 val webViewIntent = WebviewActivity.createIntent(
                     activity,
                     RemoteConfigUtils.getLoyaltyPointHistoryUrl(), "Integrasi Institusi Keuangan", false, "loyalty_account", "lainnya"
                 )
                 startActivity(webViewIntent)
             }

             binding.loyaltyWidgetWithSaldoBonus.setOnWidgetClickListener {
                 AppAnalytics.trackEvent("loyalty_main_button_click",
                     AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
                 val webViewIntent = WebviewActivity.createIntent(activity,
                     RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","lainnya")
                 startActivity(webViewIntent)
             }
             binding.loyaltyWidgetWithSaldoBonus.setOnSaldoBonusClickListener {
                 AppAnalytics.trackEvent("redeem_screen_open",
                     AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.HomePage.HOMEPAGE))
                 val webViewIntent = WebviewActivity.createIntent(activity,
                     RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                 startActivity(webViewIntent)
             }
             binding.loyaltyWidgetWithSaldoBonus.setOnReferralClickListerner {
                 val webViewIntent = WebviewActivity.createIntent(activity,
                     RemoteConfigUtils.getReferralLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","homepage")
                 startActivity(webViewIntent)
             }
        }else{
             binding.loyaltyWidget.setTierClickListener{
                 AppAnalytics.trackEvent(
                     "loyalty_button_click",
                     PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA)
                 )
                 val webViewIntent = WebviewActivity.createIntent(
                     activity,
                     RemoteConfigUtils.getLoyaltyMembershipUrl(),
                     "Integrasi Institusi Keuangan",
                     false,
                     "loyalty_account",
                     "lainnya"
                 )
                 startActivity(webViewIntent)
             }

             binding.loyaltyWidget.setOnPointClickListener {
                 AppAnalytics.trackEvent("redeem_screen_open", PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
                 val webViewIntent = WebviewActivity.createIntent(
                     activity,
                     RemoteConfigUtils.getLoyaltyPointHistoryUrl(), "Integrasi Institusi Keuangan", false, "loyalty_account", "lainnya"
                 )
                 startActivity(webViewIntent)
             }

             binding.loyaltyWidget.setOnWidgetClickListener {
                 AppAnalytics.trackEvent("loyalty_main_button_click",
                     AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
                 val webViewIntent = WebviewActivity.createIntent(activity,
                     RemoteConfigUtils.getLoyaltyLandingUrl(), "Integrasi Institusi Keuangan",false,"loyalty_account","lainnya")
                 startActivity(webViewIntent)
             }

         }


        binding.assistLayout.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TRANSACTION_LIST, AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA))
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TRANSACTION_LIST, requireActivity())
            startActivity(
                WebviewActivity.createIntent(
                    activity, if (RemoteConfigUtils.shouldAssistUrlAppendBook()) RemoteConfigUtils.getAssistUrl() + bookId else RemoteConfigUtils.getAssistUrl(),
                    getString(R.string.help_center)
                )
            )
        }
        val profileTabFeedbackInfo = RemoteConfigUtils.fetchProfileTabFeedbackInfo()
        binding.feedbackLayout.visibility = profileTabFeedbackInfo?.showView.asVisibility()
        binding.divider10.visibility = profileTabFeedbackInfo?.showView.asVisibility()
        binding.feedbackLayout.singleClick {
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_OPEN_FEEDBACK,
                AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA)
            )
            SurvicateAnalytics.invokeEventTracker(
                AnalyticsConst.EVENT_OPEN_FEEDBACK,
                requireActivity()
            )
            if (profileTabFeedbackInfo?.showOnMweb.isTrue) {
                startActivity(
                    WebviewActivity.createIntent(
                        activity, profileTabFeedbackInfo?.redirectionUrl.orEmpty(),
                        getString(R.string.send_suggestions)
                    )
                )
            } else {
                try {
                    val browserIntent = Intent(Intent.ACTION_VIEW, Uri.parse(profileTabFeedbackInfo?.redirectionUrl.orEmpty()))
                    requireContext().startActivity(browserIntent)
                } catch (ex: ActivityNotFoundException) {
                    Toast.makeText(context, "Browser Not Found", Toast.LENGTH_SHORT).show()
                }
            }
        }
        binding.settingsLayout.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_SETTINGS_SCREEN)
            startActivity(Intent(context, SettingsActivity::class.java))
        }
        binding.stickerLayout.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_STICKER_SCREEN)
            startActivity(Intent(context, StickerMainActivity::class.java))
        }
        binding.helpLayout.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN)
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_TUTORIAL_SCREEN, requireActivity())
            HelpDialog(requireContext()).show()
        }

        checkForWhatsappInstagram()

        binding.llAppUpdate.setOnClickListener {
            viewModel.onEventReceived(ProfileTabViewModel.Event.OnUpdateClicked)
        }

        binding.shareAppLayout.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_SHARE_APP_SCREEN)
            startActivity(Intent(context, ShareApp::class.java))
        }
        binding.tvSetupPrinter.setOnClickListener {
            val entryPoint = AnalyticsConst.LAINNYA
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_OPEN_PRINTER_SETUP_ACTIVITY,
                PropBuilder().put(AnalyticsConst.ENTRY_POINT, entryPoint)
            )
            val intent = Intent(
                activity,
                SetupPrinterActivity::class.java
            ).apply { putExtra(AnalyticsConst.ENTRY_POINT, entryPoint) }
            startActivity(intent)
        }

        binding.selfRemainderBtn.setOnClickListener {
            if (reminderIsEmpty) {
                startActivity(Intent(context, SetSelfReminderActivity::class.java))
            } else {
                startActivity(Intent(context, SelfReminderActivity::class.java))
            }
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_SELF_REMINDER_OPEN)
        }

        binding.tokokoBtn.setOnClickListener {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse(AppConfigManager.getInstance().tokokoDownloadLink)
                )
            )
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_CLICK_TOKOKO_DOWNLOAD)
        }
        binding.referralBtn.setOnClickListener {
            startActivity(Intent(context, ReferralActivity::class.java))
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFERRAL_OPEN)
        }
        binding.igBtn.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_IG_BW)
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(AppConst.IG_BW_URL)))
        }
        binding.fbBtn.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_FB_BW)
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(AppConst.FB_BW_URL)))
        }
        binding.txtJoinFb.setOnClickListener {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_FB_GROUP)
            SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_OPEN_FB_GROUP, requireActivity())
            startActivity(Intent(Intent.ACTION_VIEW, Uri.parse(AppConst.FB_BW_GROUP_URL)))
        }
        binding.buttonHistory.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab("")
            } else {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_LAINNYA_HISTORY)
                context?.run {
                    startActivity(
                        OrderHistoryActivity.createIntent(this, bookId)
                    )
                }
            }
        }
        binding.buttonPaymentIn.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.PAYMENT_IN)
            } else {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_LAINNYA_TAGIH_INWARDS)
                redirect(
                    PaymentCheckoutSignalHandler.getPaymentLink(
                        paymentType = PaymentHistory.TYPE_PAYMENT_IN,
                        entryPoint = AnalyticsConst.LAINNYA,
                        from = AnalyticsConst.LAINNYA
                    ), "", {}, {}
                )
            }
        }
        binding.buttonPaymentOut.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.PAYMENT_OUT)
            } else {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_LAINNYA_BAYAR_OUTWARDS)
                redirect(
                    PaymentCheckoutSignalHandler.getPaymentLink(
                        paymentType = PaymentHistory.TYPE_PAYMENT_OUT,
                        entryPoint = AnalyticsConst.LAINNYA,
                        from = AnalyticsConst.LAINNYA
                    ),"", {}, {}
                )
            }
        }
        binding.seeProfileBtn.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.PROFILE_BTN)
            } else {

                val propBuilder = PropBuilder()
                    .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA_MENU)
                    .put(AnalyticsConst.PROFILE_TYPE, AnalyticsConst.USER_PROFILE)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PROFILE_VISIT_SETTING, propBuilder)

                var intent = Intent(context, UserProfileActivity::class.java)
                intent.putExtra(USER_PROFILE_TEMP, userProfileTemp)
                intent.putExtra(SHOW_EDIT_PROFILE, false)
                startActivity(intent)
            }
        }
//        binding.editImageIcon.setOnClickListener { showImageSelectorDialog() }
//        binding.profilePic.setOnClickListener { showImageSelectorDialog() }
        binding.menuIcon.setOnClickListener {
            if (activity != null) {
                (activity as MainActivity).handleSideMenuIconClick()
            }
        }
        binding.settingsHeader.setOnClickListener {
            val newVisibility =
                if (binding.settingsSubmenu.visibility == View.VISIBLE) View.GONE else View.VISIBLE
            setSettingSubmenuVisibility(newVisibility)
            if (newVisibility == View.VISIBLE) {
                setHelpSubmenuVisibility(View.GONE)
                setBusinessCardVisibility(View.GONE)
            }
        }
        binding.helpHeader.setOnClickListener {
            var newVisibility =
                if (binding.helpSubmenu.visibility == View.VISIBLE) View.GONE else View.VISIBLE

            setHelpSubmenuVisibility(newVisibility)
            if (newVisibility == View.VISIBLE) {
                setSettingSubmenuVisibility(View.GONE)
                setBusinessCardVisibility(View.GONE)
            }
        }
        binding.businessCardPreview.root.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {
                openBusinessCard()
                val propBuilder = PropBuilder()
                    .put(
                        AnalyticsConst.IS_BUSINESS_CARD_TEMPLATE_NEW,
                        RemoteConfigUtils.NewBusinessCard.isEnabled()
                    )
                    .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.PREVIEW)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_BUSINESS_CARD_VIEW, propBuilder)
            }
        }
        binding.createCardBtn.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {
                openBusinessCard()
                val propBuilder = PropBuilder()
                    .put(
                        AnalyticsConst.IS_BUSINESS_CARD_TEMPLATE_NEW,
                        RemoteConfigUtils.NewBusinessCard.isEnabled()
                    )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_BUSINESS_CARD_INITIALIZE, propBuilder)
            }
        }

        binding.ivShare.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {

                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.OTHER)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW,
                    propBuilder
                )
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW, activity as MainActivity)
                ImageUtils.saveLayoutConvertedImage(binding.businessCardPreview.root, false)
                    .continueWith(
                        TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(activity, "", null, false, false)
                    )
            }
        }

        binding.btnDownload.setOnClickListener {
            if (!PermissonUtil.hasStoragePermission() && VERSION.SDK_INT >= 23) {
                requestPermissions(
                    PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                    PermissionConst.WRITE_EXTERNAL_STORAGE
                )
            } else {
                downLoadBusinessCard()
                Snackbar.make(it, "File berhasil diunduh", Snackbar.LENGTH_LONG).show()
            }
        }

        binding.ivInstagram.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.INSTAGRAM)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW,
                    propBuilder
                )
                ImageUtils.saveLayoutConvertedImage(binding.businessCardPreview.root, false)
                    .continueWith(
                        TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(activity, "", null, false, true)
                    )
            }
        }

        binding.ivWhatsapp.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {
                val propBuilder = PropBuilder()
                propBuilder.put(AnalyticsConst.SHARED_VIA, AnalyticsConst.WHATSAPP)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_BUSINESS_CARD_SHARE_PREVIEW,
                    propBuilder
                )
                ImageUtils.saveLayoutConvertedImage(binding.businessCardPreview.root, false)
                    .continueWith(
                        TaskExecutors.MAIN_THREAD,
                        LayoutToImageHandler(activity, "com.whatsapp", null, true, false)
                    )
            }
        }

        binding.blurCard.setOnClickListener {
            if (SessionManager.getInstance().isGuestUser) {
                listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
            } else {
                openBusinessCard()
                val propBuilder = PropBuilder()
                    .put(
                        AnalyticsConst.IS_BUSINESS_CARD_TEMPLATE_NEW,
                        RemoteConfigUtils.NewBusinessCard.isEnabled()
                    )
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_BUSINESS_CARD_INITIALIZE, propBuilder)
            }
        }
        binding.registerBankBtn.setOnClickListener {
            registerBankAccountClicked()
        }
        binding.registerBankBtn2.setOnClickListener {
            registerBankAccountClicked()
        }
        binding.listBankLayout.setOnClickListener {
            context?.run {
                AppConfigManager.getInstance().setBankLayoutBaru()
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_OPEN_USER_BANK_LIST)
                val i = BankAccountListActivity.createIntent(
                    requireActivity(),
                    PaymentConst.TYPE_PAYMENT_IN.toString(),
                    bookId,
                    AnalyticsConst.LAINNYA_DIRECT_EXISTING,
                    isSelfOnly = "true"
                )
                startActivity(i)
            }
        }

        binding.invoiceSettingLayout.setOnClickListener {
            AppConfigManager.getInstance().setAppNotaBaru()
            binding.invoiceLabel.visibility = View.GONE
            val eventProp = PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA)
            AppAnalytics.trackEvent(AnalyticsConst.INVOICE_SETTING_OPEN, eventProp)
            startActivity(InvoiceSettingActivity.createIntent(requireContext()))
        }

        binding.stockFeature.setOnClickListener {
            viewModel.onEventReceived(ProfileTabViewModel.Event.OnStockTabClicked)
            if (!binding.stockFeature.isChecked)
                binding.stockFeature.isChecked = true
        }

        binding.missionAchievements.setOnClickListener {
            streaksOnClickHandler()
        }

        val referralFloatingButtonRedirection = RemoteConfigUtils.ReferralFloatingFeature.floatingButtonRedirection()
        val referralFloatingButtonRedirectionType = RemoteConfigUtils.ReferralFloatingFeature.floatingButtonRedirectionType()

        lastTime = System.currentTimeMillis()

        val floatingImageUrl = RemoteConfigUtils.getFloatingReferralImage()

        context?.let {
            Glide.with(it).load(floatingImageUrl).placeholder(R.drawable.undang_float).into(
                binding.ibUtangFloatEntry
            )
        }

        binding.ibUtangFloatEntry.setOnClickListener {
            lastTime = System.currentTimeMillis()
            viewModel.onEventReceived(ProfileTabViewModel.Event.OnFloatingButtonHide(false))
            if (referralFloatingButtonRedirectionType.equals(AppConst.LEADERBOARD_STR)) {
                val url = AppConfigManager.getInstance().referralLeaderboardUrl
                val webViewIntent = createIntent(
                    context,
                    url,
                    getString(R.string.leaderboard_program),
                    "floating_icon"
                )
                webViewIntent.putExtra(
                    LeaderboardWebviewActivity.WEBVIEW_PARAM_IS_LEADERBOARD,
                    true
                )
                startActivity(webViewIntent)
            } else if (referralFloatingButtonRedirection.isNullOrEmpty()) {
                val prop = PropBuilder()
                prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.FLOATING_ICON)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFERRAL_OPEN, prop)
                startActivity(Intent(context, ReferralActivity::class.java))
            } else {
                WebviewActivity.createIntent(
                    context,
                    referralFloatingButtonRedirection,
                    getString(R.string.referral_program)
                )
            }
        }

        binding.vwUndangFloatEntry.setOnClickListener {
            binding.clUndangFloatEntry.animate().translationX(130f).setDuration(100).start()
            val prop = PropBuilder()
            prop.put(AnalyticsConst.ICON_NAME, AnalyticsConst.MONETORY_BONUS)
            prop.put(AnalyticsConst.ICON_POSITION, AnalyticsConst.BOTTOM_RIGHT)
            prop.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.LAINNYA_MENU)
            prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.REFERRAL_HOME_PAGE)
            prop.put(AnalyticsConst.DISMISS_METHOD, AnalyticsConst.CLICK)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_FLOATING_ICON_DISMISS, prop)

            binding.tvUndangFloatEntry.visibility = View.VISIBLE
            binding.vwUndangFloatEntry.visibility = View.GONE
        }

        binding.tvUndangFloatEntry.setOnClickListener {
            val prop = PropBuilder()
            prop.put(AnalyticsConst.ICON_NAME, AnalyticsConst.MONETORY_BONUS)
            prop.put(AnalyticsConst.ICON_POSITION, AnalyticsConst.BOTTOM_RIGHT)
            prop.put(AnalyticsConst.SCREEN_NAME, AnalyticsConst.LAINNYA_MENU)
            prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.REFERRAL_HOME_PAGE)
            prop.put(AnalyticsConst.DISMISS_METHOD, AnalyticsConst.CLOSE)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_FLOATING_ICON_DISMISS, prop)

            binding.clUndangFloatEntry.visibility = View.GONE
        }

        viewModel.getUserProfile(User.getUserId()).observe(this, Observer {
            if (it == null) {
                if (SessionManager.getInstance()
                        .isGuestUser()
                ) {
                    binding.txtOwnerName.setText(getString(R.string.default_owner_name))
                } else {
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    userProfileTemp = UserProfileEntity(
                        User.getUserId(),
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        ),
                        User.getUserId()
//                        userProfileImageUrl = bookEntity.businessImage
                    )
                    if (userProfileTemp?.userName.isNullOrEmpty()) {
                        binding.txtOwnerName.setText(getString(R.string.default_owner_name))
                    } else {
                        binding.txtOwnerName.setText(userProfileTemp?.userName)
                    }
                    binding.phoneNumber.setText(userProfileTemp?.userPhone)
//                    setProfilePic(userProfileTemp.userProfileImageUrl)

//                    viewModel.setUserProfile(userProfileTemp)
                }
            } else {
                if (it.userName.isNullOrEmpty()) {
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    binding.txtOwnerName.setText(
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        )
                    )
                } else {
                    binding.txtOwnerName.setText(it.userName)
                }
                binding.phoneNumber.setText(it.userPhone)
                setProfilePic(it.userProfileImageUrl)
                getProgressBarBlock(it)
            }
        })

        businessCardViewModel.bookEntity.observe(this, Observer {
            bookEntity = it
        })

        businessCardViewModel.businessCardDesignLiveData.observe(this, Observer {
            if (it.size > 0) {
                val cardDesigns = it
                if (savedBizzCardDesign == null) {
                    savedBizzCardDesign = cardDesigns.get(0)
                }
                val savedBizzCardUid = FeaturePrefManager.getInstance().newBizzCardDesign
                savedBizzCardDesign =
                    cardDesigns.find { design -> design.cardUID == savedBizzCardUid }
                        ?: return@Observer
            }
        })

        val shouldShowNewHomePage = RemoteConfigUtils.NewHomePage.shouldShowNewHomePage()

        binding.apply {
            if (shouldShowNewHomePage) {
                binding.gpStock.hideView()
                paymentCard.hideView()
                gradientBgView.hideView()
                paymentInfoMessage.rootLayout.hideView()
                divider.hideView()
                vpProfileBanner.hideView()
                tbProfileBanner.hideView()
                inProfilePins.root.hideView()
                dividerPinBottom.hideView()
                divider1.hideView()
                settingsHeaderGroup.hideView()
                registerBankLayout.hideView()
                registerBankLayout2.hideView()
                barrierPaymentRegisterBank.hideView()
                invoiceSetting.hideView()
                invoiceLabel.hideView()
                gpSticker.hideView()
                helpSubmenu.showView()
            } else {
                binding.gpStock.showView()
                paymentCard.showView()
                gradientBgView.showView()
                paymentInfoMessage.rootLayout.showView()
                divider.showView()
                vpProfileBanner.showView()
                tbProfileBanner.showView()
                inProfilePins.root.showView()
                dividerPinBottom.showView()
                divider1.showView()
                settingsHeaderGroup.showView()
                registerBankLayout2.showView()
                barrierPaymentRegisterBank.showView()
                invoiceSetting.showView()
                invoiceLabel.showView()
                gpSticker.showView()
                helpSubmenu.hideView()
            }
        }
        setupRefereeEntryPoint()
    }

    private fun setupRefereeEntryPoint() {
        val jsonType = object : TypeToken<RefreeEntryPointData?>() {}.type
        val refereeEntryPointData: RefreeEntryPointData? =
            jsonType.returnObject(RemoteConfigUtils.getRefereePointEntryData())
        if (refereeEntryPointData != null) {
            binding.incentiveLayout.refereeEntryPoint.tvInfo.text = refereeEntryPointData.title
        }
        binding.incentiveLayout.refereeEntryPoint.root.setOnClickListener {
            if (refereeEntryPointData != null) {
                context?.let {
                    val intent = WebviewActivity.createIntent(
                        it,
                        BuildConfig.API_BASE_URL_LOYALTY.plus(refereeEntryPointData.redirection),
                        AppConst.EMPTY_STRING
                    )
                    startActivity(intent)
                }
            }
        }
    }

    private fun resolveDestinationCarousel(progressBarBlock: List<ProgressBarItemLiannya>, position: Int) {
        val type = progressBarBlock[position].type

        when (type) {
            USER_PROFILE -> {
                val propBuilder = PropBuilder()
                    .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA_MENU)
                    .put(AnalyticsConst.PROFILE_TYPE, AnalyticsConst.USER_PROFILE)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PROFILE_VISIT_SETTING, propBuilder)
                val intent = Intent(context, UserProfileActivity::class.java)
                intent.putExtra(USER_PROFILE_TEMP, userProfileTemp)
                intent.putExtra(SHOW_EDIT_PROFILE, true)
                val isProfileCompleted = FeaturePrefManager.getInstance()
                    .hasCompleteUserProfile()
                propBuilder.put("type", AnalyticsConst.USER_PROFILE).put("status", if (isProfileCompleted) "complete" else "incomplete")

                val request: CompletionRequest = CompletionRequest(AppConst.USER_PROFILE_COMPLETION)
                if (isProfileCompleted) {
                    viewModel.onEventReceived(ProfileTabViewModel.Event.ProfileCompletionEvent(request))
                }

                AppAnalytics.trackEvent("progress_bar_clicked", propBuilder)
                startActivity(intent)
            }
            USER_BUSINESS_PROFILE -> {
                val isBusinessProfileCompleted = FeaturePrefManager.getInstance()
                    .hasCompleteBusinessProfile()
                val propBuilder = PropBuilder().put("type", AnalyticsConst.BUSINESS_PROFILE).put("status", if (isBusinessProfileCompleted) "complete" else "incomplete")

                val request = CompletionRequest(AppConst.BUSINESS_PROFILE_COMPLETION)
                if (isBusinessProfileCompleted) {
                    viewModel.onEventReceived(ProfileTabViewModel.Event.ProfileCompletionEvent(request))
                }

                AppAnalytics.trackEvent("progress_bar_clicked", propBuilder)
                openBusinessProfileWebView()
            }
            USER_KYC_COMPLETE -> {
                val redirectionUrl = progressBarBlock[position].deeplinkUrl
                val propBuilder = PropBuilder().put("type", "KYC").put(
                    "status", if (FeaturePrefManager.getInstance()
                            .hasCompletedKYC()
                    ) "complete" else "incomplete"
                )
                AppAnalytics.trackEvent("progress_bar_clicked", propBuilder)
                startActivity(
                    WebviewActivity.createIntent(
                        requireActivity(), redirectionUrl,
                        "Verifikasi Data Diri"
                    )
                )
            }
            BANK_ACCOUNT -> {
                val propBuilder = PropBuilder().put("type", "bank_saving").put(
                    "status", if (FeaturePrefManager.getInstance()
                            .hasBankAccount(User.getBusinessId())
                    ) "complete" else "incomplete"
                )
                AppAnalytics.trackEvent("progress_bar_clicked", propBuilder)
                viewModel.onEventReceived(ProfileTabViewModel.Event.OnRegisterBankAccount)
            }
            else -> {}
        }
    }

    private fun openBusinessProfileWebView() {
        if (RemoteConfigUtils.getBusinessProfileVariant() == RemoteConfigConst.BUSINESS_PROFILE_VARIANT_NEW_WEB) {
            val webViewIntent = BusinessProfileWebviewActivity.createIntent(context)
            requireContext().startActivity(webViewIntent)
        } else {
            try {
                val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                val completionPercentage = Utility.calculateCompletionPercentage(bookEntity)
                
            } catch (e: Exception) {
                FirebaseCrashlytics.getInstance().recordException(e)
            }

            val webViewIntent = NgBusinessProfileActivity.createIntent(context, User.getBusinessId())
            requireContext().startActivity(webViewIntent)
        }
    }

    private fun getProgressBarBlock(userProfileEntity: UserProfileEntity) {
        var progressBarBlockJson: String = RemoteConfigUtils.LAINNYA_PROGRESS_BAR_BLOCK.getLainnyaProgressBarBlock()
        val type = object : TypeToken<List<ProgressBarItemLiannya>>() {}.type
        val gson: Gson = GsonBuilder().create()
        var blockData: List<ProgressBarItemLiannya>
        try {
            blockData = gson.fromJson(progressBarBlockJson, type)
        } catch (e: JsonSyntaxException) {
            progressBarBlockJson = RemoteConfigUtils.LAINNYA_PROGRESS_BAR_BLOCK.getLainnyaProgressBarBlock()
            blockData = gson.fromJson(progressBarBlockJson, type)
        }
        if (visibleItemsList.isEmpty()) {
            for (item in blockData) {
                if (item.showOrHide == true) {
                    visibleItemsList.add(item)
                }
            }
        }
        if (!visibleItemsList.isNullOrEmpty()) {
            progressBarBlock = visibleItemsList.sortedBy { it.rank }
            for (progressBarItem in progressBarBlock) {
                when (progressBarItem.type) {
                    USER_PROFILE -> {
                    }
                    USER_KYC_COMPLETE -> {

                    }
                    BANK_ACCOUNT -> {
                    }
                    else -> {}
                }
            }
            businessRepository = BusinessRepository.getInstance(Application.getAppContext())
            bookEntity = businessRepository?.getBusinessByIdSync(User.getBusinessId())
            progressBarAdapter =
                LainnyaProgressBarAdapter(
                    progressBarBlock,
                    userProfileEntity,
                    bookEntity,
                    PaymentPrefManager.getInstance().getKycStatus(),
                    PaymentPrefManager.getInstance().getKybStatus()
                ) {
                    resolveDestinationCarousel(progressBarBlock, it)
                }
            binding.incentiveLayout.rvProgressBar.adapter = progressBarAdapter
            binding.incentiveLayout.rvProgressBar.scrollToPosition(0)
        } else {
            binding.incentiveLayout.root.hideView()
        }
    }


    private fun streaksOnClickHandler() {
        if (SystemClock.elapsedRealtime() - lastButtonSaveClicked < 600)
            return
        lastButtonSaveClicked = SystemClock.elapsedRealtime()
    }

    private fun downLoadBusinessCard() {
        ImageUtils.saveLayoutConvertedImage(binding.businessCardPreview.root, true)

        val downloadDirectory = File(
            Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS),
            "business_card.png"
        )

        val path = Uri.fromFile(downloadDirectory)
        val imageOpenIntent = Intent(Intent.ACTION_VIEW)
        imageOpenIntent.flags = Intent.FLAG_ACTIVITY_CLEAR_TOP
        if (path != null) {
            imageOpenIntent.setDataAndType(path, "image/*")
            try {
                this.startActivity(imageOpenIntent)
            } catch (e: Exception) {
            }
        }
    }

    private fun registerBankAccountClicked() {
        if (SessionManager.getInstance().isGuestUser) {
            listener?.callLoginFromProfileTab("")
            return
        }
        viewModel.onEventReceived(ProfileTabViewModel.Event.OnRegisterBankAccount)
    }

    private fun goToRegisterBankAccount() {
        context?.run {
            if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_ADD_BANK_ACCOUNT)) {
                PaymentUtils.showKycKybStatusBottomSheet(childFragmentManager, AnalyticsConst.LAINNYA)
            } else {
                val prop = PropBuilder().put(
                    AnalyticsConst.QUALIFY_FOR_PEMBAYARAN_TAB,
                    true
                )
                    .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA_DIRECT_FIRST)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_SET_USER_BANK, prop)
                val i = AddBankAccountActivity.createIntent(
                    requireActivity(),
                    PaymentConst.TYPE_PAYMENT_IN.toString(),
                    bookId,
                    AnalyticsConst.LAINNYA_DIRECT_FIRST,
                    hasBankAccount = "false",
                    paymentIn = true
                )
                startActivity(i)
            }
        }
    }

    private fun openBusinessCard() {
        if (savedBizzCardDesign == null) return

        if (bookEntity?.hasCompletedCardNoEmail() == true) {

            val propBuilder = PropBuilder()
            propBuilder.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA_MENU)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PREVIEW_BUSINESS_CARD_OPEN, propBuilder)

            val intent = Intent(context, BusinessCardShareActivity::class.java).apply {
                putExtra(BusinessCardShareActivity.NEW_CARD_DESIGN, savedBizzCardDesign)
            }
            startActivity(intent)
        } else {
            val isNewBusinessCardEnabled = RemoteConfigUtils.NewBusinessCard.isEnabled()
            val clazz = if (isNewBusinessCardEnabled) NewBusinessCardActivity::class.java else BusinessCardActivity::class.java
            startActivity(Intent(context, clazz))
        }

    }

    private fun setSettingSubmenuVisibility(visibility: Int) {
        binding.settingsSubmenu.visibility = visibility
        setHeaderMenuAttribute(binding.settingsHeader, visibility, R.drawable.ic_baseline_settings)
    }

    private fun setHelpSubmenuVisibility(visibility: Int) {
        binding.helpSubmenu.visibility = visibility
        setHeaderMenuAttribute(binding.helpHeader, visibility, R.drawable.ic_baseline_help_outline)
    }

    private fun toggleCard(shouldShowOpenState: Boolean, fromClick: Int) {
        var newVisibility =
            if (binding.bgCard.visibility == View.VISIBLE) View.GONE else View.VISIBLE

        if (fromClick == 0) {
            if (shouldShowOpenState) {
                newVisibility = View.VISIBLE
            } else {
                newVisibility = View.GONE
            }

            businessCardVisibility(newVisibility)
            return
        }

        businessCardVisibility(newVisibility)
    }

    private fun businessCardVisibility(visibility: Int) {
        setBusinessCardVisibility(visibility)
        if (visibility == View.VISIBLE) {
            binding.createCardBtn.visibility = View.GONE
            setHelpSubmenuVisibility(View.GONE)
            setSettingSubmenuVisibility(View.GONE)
        }
    }

    private fun toggleCardUnfinished(shouldShowOpenState: Boolean, fromClick: Int) {
        var newVisibility =
            if (binding.bgCard.visibility == View.VISIBLE) View.GONE else View.VISIBLE
        if (fromClick == 0) {
            if (shouldShowOpenState) {
                newVisibility = View.VISIBLE
            } else {
                newVisibility = View.GONE
            }

            blurCardVisibility(newVisibility)
            return
        }

        blurCardVisibility(newVisibility)

    }

    private fun blurCardVisibility(visibility: Int) {
        setBusinessCardVisibility(visibility)
        if (visibility == View.VISIBLE) {
            setHelpSubmenuVisibility(View.GONE)
            setSettingSubmenuVisibility(View.GONE)
            binding.btnDownload.visibility = View.GONE
            binding.tvShare.visibility = View.GONE
            binding.llShare.visibility = View.GONE
            binding.createCardBtn.visibility = View.VISIBLE
            if (!hasDrawBlur) {
                binding.businessCardPreview.root.viewTreeObserver.addOnGlobalLayoutListener(object :
                    ViewTreeObserver.OnGlobalLayoutListener {
                    override fun onGlobalLayout() {
                        setBlurredCard()
                        binding.businessCardPreview.root.viewTreeObserver.removeOnGlobalLayoutListener(
                            this
                        )
                    }
                })
            } else {
                binding.blurCard.visibility = View.VISIBLE
                binding.ivBlurred.visibility = View.VISIBLE
                binding.ivCreateCardInfo.visibility = View.VISIBLE
            }
        }
    }

    private fun setBusinessCardVisibility(visibility: Int) {
        if (visibility == View.GONE) {
            binding.createCardBtn.visibility = View.GONE
            binding.blurCard.visibility = View.GONE
            binding.ivBlurred.visibility = View.GONE
            binding.ivCreateCardInfo.visibility = View.GONE
        }
        binding.bgCard.visibility = visibility
        binding.tvShare.visibility = visibility
        binding.btnDownload.visibility = visibility
        binding.llShare.visibility = visibility
        binding.businessCardPreview.root.visibility = visibility
        setHeaderMenuAttribute(binding.businessCardBtn, visibility, R.drawable.ic_kartu_nama)
    }

    private fun setHeaderMenuAttribute(textView: TextView, visibility: Int, drawableLeft: Int) {
        val arrow: Int
        val typeFace: Int
        if (visibility == View.VISIBLE) {
            arrow = R.drawable.ic_chevron_up
            textView.setBackgroundResource(R.drawable.header_menu_item_background)
            typeFace = Typeface.BOLD
        } else {
            arrow = R.drawable.ic_chevron_down
            val attr = TypedValue()
            context?.theme?.resolveAttribute(android.R.attr.selectableItemBackground, attr, true)
            textView.setBackgroundResource(attr.resourceId)
            typeFace = Typeface.NORMAL
        }
        textView.setTypeface(
            binding.shareAppLayout.typeface,
            typeFace
        ) // shareAppLayout just as reference to unchanged typeface
        textView.setDrawable(right = arrow, left = drawableLeft)

    }

    private fun setProfilePic(imageUri: String?) {
        Glide.with(this).load(imageUri)
            .apply(RequestOptions().circleCrop())
            .placeholder(R.drawable.ic_icon_shop)
            .error(R.drawable.ic_icon_shop)
            .into(binding.profilePic)
    }

    private fun initProfileView(
        trxCount: Int, bookEntity: BookEntity?, hasPendingPayment: Boolean, phoneNumber: String,
        hasEditCard: Boolean, showPpobBanner: Boolean
    ) {
        try {
            val visibility = if (trxCount > 0) View.VISIBLE else View.GONE

            if (trxCount >= 1) {
                try {
                    if (!Utility.isBlank(AppConfigManager.getInstance().getNotificationIcon())) {
                        InfoDiscoveryAdapter.LoadImage(
                            binding.notificationIcon,
                            AppConfigManager.getInstance().getNotificationIcon(),
                            false
                        ).execute()
                    }
                } catch (e: Exception) {
                    e.printStackTrace()
                }
                binding.notificationIcon.setVisibility(View.VISIBLE);

                if (!FeaturePrefManager.getInstance().hasUnseenNotification()) {
                    binding.notifyHighlighterExp.setVisibility(View.GONE);
                } else {
                    try {
                        val notificationList =
                            BusinessRepository.getInstance(getContext()).getNotificationsList();
                        val filteredNotificationList = mutableListOf<AppNotification>()
                        for (appNotification: AppNotification in notificationList) {
                            if (trxCount >= appNotification.minTrxToShow) {
                                filteredNotificationList.add(appNotification);
                            }
                        }

                        if (!filteredNotificationList.isEmpty()) {
                            binding.notifyHighlighterExp.setVisibility(View.VISIBLE);
                            binding.notifyHighlighterExp.setText(filteredNotificationList.size.toString())
                        }
                    } catch (ex: Exception) {
                        ex.printStackTrace()
                    }
                }
            } else {
                binding.notificationIcon.setVisibility(View.GONE);
                binding.notifyHighlighterExp.setVisibility(View.GONE);
            }

            if (RemoteConfigUtils.getBellBannerTab() == TabName.OTHERS.name) {
                binding.notificationIcon.setVisibility(View.VISIBLE);
            } else {
                binding.notificationIcon.setVisibility(View.GONE);
                binding.notifyHighlighterExp.setVisibility(View.GONE)
            }

            binding.notificationIcon.setOnClickListener(View.OnClickListener { view: View? ->
                val prop = AppAnalytics.PropBuilder()
                prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA)
                AppAnalytics.trackEvent(AnalyticsConst.OPEN_NOTIFICATION, prop)
                FeaturePrefManager.getInstance().setUnseenNotification(false)
                binding.notifyHighlighterExp.setVisibility(View.GONE)
                startActivity(Intent(context, NotificationActivity::class.java))
            })

            binding.settingsHeaderGroup.visibility = View.VISIBLE
            if (visibility == View.VISIBLE) {
                FeaturePrefManager.getInstance().setProfileVisitCount();
            }
            bookEntity?.run {
                <EMAIL> = this.bookId
                val name: String
                if (!FeaturePrefManager.getInstance().isSimpanForBusinessCardClicked) {
                    name = getString(R.string.mybusiness)
                    binding.seeProfileBtn.text = getString(R.string.setup_profile)
                    binding.seeProfileBtn.setStyleButtonFill(context)
                    if (!hasEditCard) {
                        if (trxCount > 0) {
                            toggleCardUnfinished(false, 0)
                        }
                        binding.businessCardBtn.setOnClickListener {
                            toggleCardUnfinished(false, 1)
                        }
                    } else {
                        binding.businessCardBtn.setOnClickListener { toggleCard(false, 1) }
                        if (binding.blurCard.visibility == View.VISIBLE && trxCount > 0) {
                            toggleCardUnfinished(false, 1)
                        }
                    }
                } else {
                    name = businessName
                    binding.seeProfileBtn.text = getString(R.string.edit)
                    binding.seeProfileBtn.setStyleButtonOutline(context)


                    binding.businessCardBtn.setOnClickListener { toggleCard(false, 1) }
                    if (binding.blurCard.visibility == View.VISIBLE && trxCount > 0) {
                        toggleCardUnfinished(false, 1)
                    }
                }
                if (Utility.hasBusinessName(businessName)) {
                    binding.screenTitle.text = businessName
                } else {
                    binding.screenTitle.text = name
                }
//                binding.txtOwnerName.text = if (SessionManager.getInstance()
//                        .isGuestUser()
//                ) "Nama Saya" else businessOwnerName
//                if (binding.txtOwnerName.text.length == 0) {
//                    binding.txtOwnerName.visibility = View.GONE
//                    binding.phoneNumber.visibility = View.GONE
//                    binding.phoneNumber2.visibility = View.VISIBLE
//                } else {
//                    binding.txtOwnerName.visibility = View.VISIBLE
//                    binding.phoneNumber.visibility = View.VISIBLE
//                    binding.phoneNumber2.visibility = View.GONE
//                }
                initCardValues(this)
//                setProfilePic(this.businessImage)
            }
//            binding.phoneNumber.text = if (SessionManager.getInstance().isGuestUser()) "-" else phoneNumber
//            binding.phoneNumber2.text = if (SessionManager.getInstance().isGuestUser()) "-" else phoneNumber
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun initCardValues(bookEntity: BookEntity) {
        try {
            binding.businessCardPreview.nameLayout.visibility =
                if (bookEntity.businessOwnerName.isNullOrBlank()) View.GONE else View.VISIBLE
            binding.businessCardPreview.phoneLayout.visibility =
                if (bookEntity.businessPhone.isNullOrBlank()) View.GONE else View.VISIBLE
            binding.businessCardPreview.emailLayout.visibility =
                if (bookEntity.businessEmail.isNullOrBlank()) View.GONE else View.VISIBLE
            binding.businessCardPreview.addrLayout.visibility =
                if (bookEntity.businessAddress.isNullOrBlank()) View.GONE else View.VISIBLE
            binding.businessCardPreview.name.text = bookEntity.businessName
            binding.businessCardPreview.phone.text =
                Utility.beautifyPhoneNumber(bookEntity.businessPhone)
            binding.businessCardPreview.email.text = bookEntity.businessEmail
            binding.businessCardPreview.address.text = bookEntity.businessAddress
            binding.businessCardPreview.caption.text = bookEntity.businessTagLine
            binding.businessCardPreview.owner.text = bookEntity.businessOwnerName
            viewModel.onEventReceived(ProfileTabViewModel.Event.GetCardBackground)
        } catch (e: java.lang.Exception) {
            e.recordException()
        }
        try {
            if (!FeaturePrefManager.getInstance().hasSentBusinessInfo() && Utility.hasBusinessName(
                    bookEntity.businessOwnerName
                )
            ) {
                AppAnalytics.setUserProperty("business_type", bookEntity.bookType.toString())
                AppAnalytics.setUserProperty("business_type_name", bookEntity.bookTypeName)
                AppAnalytics.setUserProperty("business_name", bookEntity.businessName)
                FeaturePrefManager.getInstance().hasSentBusinessInfo(true)
            }
        } catch (e: java.lang.Exception) {
            e.recordException()
        }
    }


    private fun setBlurredCard() {
        context?.run {
            val originalCard = Utility.captureScreenshot(binding.businessCardPreview.root)
            binding.blurredImg.setImageBitmap(Utility.blur(this, originalCard))
            binding.blurCard.visibility = View.VISIBLE
            binding.ivBlurred.visibility = View.VISIBLE
            binding.ivCreateCardInfo.visibility = View.VISIBLE
            hasDrawBlur = true
        }
    }

    override fun onResume() {
        super.onResume()
        viewModel.getUserProfile(User.getUserId()).observe(this, Observer {
            if (it == null) {
                if (SessionManager.getInstance()
                        .isGuestUser()
                ) {
                    binding.txtOwnerName.setText(getString(R.string.default_owner_name))
                } else {
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    userProfileTemp = UserProfileEntity(
                        User.getUserId(),
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        ),
                        User.getUserId()
                    )
                    if (userProfileTemp?.userName.isNullOrEmpty()) {
                        binding.txtOwnerName.setText(getString(R.string.default_owner_name))
                    } else {
                        binding.txtOwnerName.setText(userProfileTemp?.userName)
                    }
                    binding.phoneNumber.setText(userProfileTemp?.userPhone)
                }
            } else {
                if (it.userName.isNullOrEmpty()) {
                    val bookEntity: BookEntity = viewModel.getBusinessByIdSync(User.getBusinessId())
                    binding.txtOwnerName.setText(
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        )
                    )
                } else {
                    binding.txtOwnerName.setText(it.userName)
                }
                binding.phoneNumber.setText(it.userPhone)
                setProfilePic(it.userProfileImageUrl)
                getProgressBarBlock(it)
                if (it.dateOfBirth.isNotNullOrEmpty()
                    && it.userName.isNotNullOrEmpty()
                    && it.userEmail.isNotNullOrEmpty()
                    && it.userPhone.isNotNullOrEmpty()
                ) {
                    FeaturePrefManager.getInstance().hasCompleteUserProfile(true)
                }
            }
        })
/*  Logic to add hide all the cards

//        if (!FeaturePrefManager.getInstance().isLainnyaProfileCompleted ) {
//            for (item in visibleItemsList) {
//                if (FeaturePrefManager.getInstance().getFeatureCompletionById(item.type, )) {
//                    isAllSectionsCompleted = true
//                } else {
//                    isAllSectionsCompleted = false
//                    break
//                }
//            }
//            if (isAllSectionsCompleted) {
//                FeaturePrefManager.getInstance().isLainnyaProfileCompleted = true
//                UserProfileMissionSuccessBottomSheet(isFromLainnyaTab = true){
//                }.show(
//                    childFragmentManager,
//                    "user-profile-mission-complete"
//                )
//                binding.incentiveLayout.root.hideView()
//            }
//        }

 */
        viewModel.onEventReceived(
            ProfileTabViewModel.Event.OnTransactionCount(
                TransactionRepository
                    .getInstance(activity).transactionCount + FeaturePrefManager.getInstance().paymentTransactionCount
            )
        )

        viewModel.onEventReceived(
            ProfileTabViewModel.Event.OnFloatingButtonThreshold(
                TransactionRepository.getInstance(activity).countCashTransactionsWithDeleted()
            )
        )


        if (AppConfigManager.getInstance().isNotaBaru) {
            binding.invoiceLabel.visibility = View.GONE
        }
        if (AppConfigManager.getInstance().isBankLayoutBaru) {
            binding.listBankNewLabel.visibility = View.GONE
        }

        if (System.currentTimeMillis() - lastTime > 5000 || firstTimeOnResume) {
            viewModel.onEventReceived(ProfileTabViewModel.Event.OnFloatingButtonHide(true))
        }

        if (!firstTimeOnResume) {
            viewModel.onEventReceived(ProfileTabViewModel.Event.OnCreateView)
        } else {
            firstTimeOnResume = false
            if (binding.listBankGroup.visibility == View.VISIBLE)
                viewModel.onEventReceived(ProfileTabViewModel.Event.CheckForCoachmark)
        }

        businessCardViewModel.refreshBookData()
        setPrinterCountText()
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        try {
            if (requestCode == AppConst.TO_TUTORIAL_FROM_PROFILE_TAB_CODE) {
                if (resultCode == Activity.RESULT_OK) {
                    activity?.supportFragmentManager?.run {
                        TutorialCompletionDialog.show(this)
                    }
                }
                return
            }
            var uri: Uri? = null
            if (resultCode == Activity.RESULT_OK && requestCode == PermissionConst.TAKE_PHOTO) {
                uri = profilePicUri
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            } else if (requestCode == PermissionConst.REQ_PICK_IMAGE_PERMISSON && resultCode == -1 && intent != null && intent.data != null) {
                uri = intent.data
            }

            uri?.let {
                var bitmap = MediaStore.Images.Media.getBitmap(
                    requireActivity().contentResolver,
                    uri
                )


                bitmap = Utility.fixImageRotation(requireContext(), bitmap, profilePicFile, it)
                profilePicFile = null

                viewModel.onEventReceived(ProfileTabViewModel.Event.OnUploadImage(bitmap, uri))
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    private fun showImageSelectorDialog() {
        if (context == null) return
        val imageSelectorDialog = ImageSelectorDialog2(requireContext(), {
            setImageSourceFromCamera()
        }, {
            getImageFromGallery()
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(requireActivity().packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setImageSourceFromCamera()
                }
            }
            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    getImageFromGallery()
                }
            }


            PermissionConst.WRITE_EXTERNAL_STORAGE -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    downLoadBusinessCard()
                }
            }
            else -> {}
        }
    }

    private fun getImageFromGallery() {
        if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
            return
        }

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        startActivityForResult(
            Intent.createChooser(
                intent,
                getString(R.string.select_image_instruction)
            ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
        )
    }

    private fun handleUpdateProcess(useFlexibleUpdate: Boolean) {
        if ((activity as MainActivity).isUpdateAvailable) {
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_LAINNYA_UPDATE_APP)
            activity?.let {
                launchPlayStoreWithUri(it, BuildConfig.PLAYSTORE_URL)
            }
        } else {
            NotificationUtils.alertToast(getString(R.string.latest_version_app))
        }
    }

    override fun onOnboardingDismiss(
        id: String?,
        body: String,
        isFromButton: Boolean,
        isFromCloseButton: Boolean,
        isFromOutside: Boolean
    ) {
    }

    override fun onOnboardingButtonClicked(id: String?, isFromHighlight: Boolean) {
        //do nothing
    }

    fun onBackPressed() {
        if (onboardingWidget != null && onboardingWidget!!.isShown) {
            onboardingWidget!!.dismiss(
                isFromButton = false,
                isFromCloseButton = false,
                isFromOutside = true
            )
        } else if (activity != null && listener != null) {
            listener?.handleNormalBackPressedFromProfile()
        } else if (activity != null) {
            requireActivity().finish()
        }
    }

    private fun showBankListCoachmark(hasShownListBankCoachmark: Boolean) {
        if (!hasShownListBankCoachmark && !isListBankCoachmarkShown && binding.listBankGroup.visibility == View.VISIBLE) {
            isListBankCoachmarkShown = true
            viewModel.onEventReceived(ProfileTabViewModel.Event.SetShownListBankCoachmark)
            onboardingWidget = OnboardingWidget.createInstance(
                requireActivity(),
                this,
                OnboardingPrefManager.TUTOR_BANK_LIST_LAINNYA,
                binding.listBankLayout,
                R.drawable.onboarding_announce,
                getString(R.string.new_feature),
                getString(R.string.onboarding_list_bank_subtitle),
                "",
                FocusGravity.CENTER,
                ShapeType.RECTANGLE_FULL,
                1,
                1,
                true,
                true,
                0
            )
        }
    }

    private fun showAutoRecordIntroTooltip() {
        builder(requireContext())
            .setGravity(Gravity.BOTTOM)
            .setText(requireContext().getString(R.string.come_on_try_new_auto_record_feature))
            .setAnchor(binding.autoTxnLayout)
            .setWidth(null)
            .build()
            .show()

    }

    private fun checkForWhatsappInstagram() {
        val packageManager = requireActivity().packageManager
        var isInstalled = ShareUtils.isPackageInstalled("com.whatsapp", packageManager)

        if (!isInstalled) {
            binding.ivWhatsapp.visibility = View.GONE
        }

        isInstalled = ShareUtils.isPackageInstalled("com.instagram.android", packageManager)
        if (!isInstalled) {
            binding.ivInstagram.visibility = View.GONE
        }

    }

    private fun resolveDestination(pins: List<ProfilePins>, position: Int) {

        val deeplink = pins[position].deeplink
        var redirectionUrl = pins[position].deeplink_url

        val prop = PropBuilder()
        prop.apply {
            put(AnalyticsConst.ROW, position / getColumnCount() + 1)
            put(AnalyticsConst.COLUMN, position % getColumnCount() + 1)
            put(AnalyticsConst.BUTTON, pins[position].readabaleName)
        }

        val transactionCount = TransactionRepository.getInstance(context).transactionCountWithDeletedRecords

        if (transactionCount < 1 && deeplink != 6 && position != 0) {
            return
        }

        redirect(
            redirection = redirectionUrl,
            title = pins[position].deeplink_url,
            onSuccess = {
                prop.put(AnalyticsConst.REDIRECTED_TO, pins[position].deeplink_url)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_LAINNYA_WIDGET_CLICK, prop)
            },
            onFailure = {
                when (deeplink) {
                    1 -> if (SessionManager.getInstance().isGuestUser) {
                        listener?.callLoginFromProfileTab(AnalyticsConst.BUSINESS_CARD)
                    } else {
                        openBusinessCard()
                        val propBuilder = PropBuilder()
                            .put(
                                AnalyticsConst.IS_BUSINESS_CARD_TEMPLATE_NEW,
                                RemoteConfigUtils.NewBusinessCard.isEnabled()
                            )
                            .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.PREVIEW)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_OPEN_BUSINESS_CARD_VIEW, propBuilder)

                        prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.BUSINESS_CARD)
                    }


                    2 -> {
                        AppAnalytics.trackEvent(
                            AnalyticsConst.EVENT_PPOB_PULSA_BUY_BUTTON,
                            PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA),
                            true,
                            false,
                            false
                        )
                        SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_PPOB_PULSA_BUY_BUTTON, requireActivity())
                        if (SessionManager.getInstance().isGuestUser) {
                            listener?.callLoginFromProfileTab("")
                        } else {
                            PpobUtils.getPpobCategoryActivityIntent(
                                    fragmentManager = childFragmentManager,
                                    context = requireContext(), category = PpobConst.CATEGORY_PULSA
                            )?.let { startActivity(it) }
                            (activity as MainActivity).enablePaymentsTab()
                            prop.put(
                                AnalyticsConst.REDIRECTED_TO,
                                AnalyticsConst.EVENT_PPOB_PULSA_BUY_BUTTON
                            )
                        }
                    }

                    3 -> {
                        if (reminderIsEmpty) {
                            startActivity(Intent(context, SetSelfReminderActivity::class.java))
                        } else {
                            startActivity(Intent(context, SelfReminderActivity::class.java))
                        }
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_SELF_REMINDER_OPEN)
                        prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.EVENT_SELF_REMINDER_OPEN)
                    }

                    4 -> {
                        startActivity(Intent(context, ReferralActivity::class.java))
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFERRAL_OPEN)
                        prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.EVENT_REFERRAL_OPEN)
                        prop.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA_TILE)
                    }

                    5 -> {
                        val eventProp =
                            PropBuilder().put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.LAINNYA)
                        AppAnalytics.trackEvent(AnalyticsConst.INVOICE_SETTING_OPEN, eventProp)
                        startActivity(InvoiceSettingActivity.createIntent(requireContext()))
                        prop.put(AnalyticsConst.REDIRECTED_TO, AnalyticsConst.INVOICE_SETTING_OPEN)
                    }

                    6 -> {
                        AppAnalytics.trackEvent("open_payments_screen")
                        MainActivity.startActivitySingleTopToTab(requireContext(), TabName.PAYMENT)
                        prop.put(AnalyticsConst.REDIRECTED_TO, "open_payments_screen")
                    }
                    else -> {}
                }

                AppAnalytics.trackEvent(AnalyticsConst.EVENT_LAINNYA_WIDGET_CLICK, prop)
            },
        )
    }

    private fun redirect(
        redirection: String,
        title: String,
        onSuccess: () -> Unit,
        onFailure: () -> Unit,
    ) {
        val sourceLink = SourceLink(context = requireContext(), link = redirection)
        neuro.route(
            sourceLink,
            navigator = this,
            onSuccess = onSuccess,
            onFailure = {
                redirectWithLegacyLink(redirection, title, onSuccess, onFailure)
            },
        )
    }

    private fun redirectWithLegacyLink(
        redirection: String,
        title: String,
        onSuccess: () -> Unit,
        onFailure: () -> Unit,
    ) {
        var url = redirection

        if (url.isNotNullOrEmpty() && url.startsWith("redirectTo:")) {
            url = url.replace("redirectTo:", "")
            val link = "$DEEPLINK_INTERNAL_URL?type=act&data=$url&launch=2"
            val sourceLink = SourceLink(context = requireContext(), link)

            neuro.route(
                sourceLink,
                navigator = this,
                onSuccess = onSuccess,
                onFailure = {},
            )
            return
        }

        if (url.isNotNullOrEmpty()
            && (url.startsWith("http") || url.startsWith("https")
                    || url.contains(BuildConfig.DEEPLINK_SCHEME))
        ) {
            // web url
            val link = "$DEEPLINK_INTERNAL_URL/web?data=$url&title=$title"
            val sourceLink = SourceLink(
                context = requireContext(),
                link = link,
            )

            neuro.route(
                sourceLink,
                navigator = this,
                onSuccess = onSuccess,
                onFailure = {},
            )
            return
        }

        onFailure()
    }

    private fun profileBannerClick(redirectionUrl: String, type: String?) {
        when {
            type.equals(AppConst.LEADERBOARD_STR) -> {
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_OPEN_MICROSITE_CAMPAIGN, AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.LAINNYA_BANNER)
                )
                val webViewIntent = createIntent(
                    context,
                    redirectionUrl,
                    getString(R.string.leaderboard_program),
                    AnalyticsConst.LAINNYA_BANNER
                )
                webViewIntent.putExtra(
                    LeaderboardWebviewActivity.WEBVIEW_PARAM_IS_LEADERBOARD,
                    true
                )
                startActivity(webViewIntent)
            }
            redirectionUrl.startsWith(DEEPLINK_SCHEME_BUKUWARUNG) -> {
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse(redirectionUrl)
                intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP)
                startActivity(intent)
                activity?.finish()
            }
            else -> {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_LENDING_BANNER_LAINNYA_MENU_CLICKED)
                val intent = WebviewActivity.createIntent(context, redirectionUrl, "")
                startActivity(intent)
            }
        }
    }

    private fun changeAppUpdateBadgeVisibility() {
        if ((activity as MainActivity).isUpdateAvailable) {
            binding.labelUpdateApp.showView()
        } else {
            binding.labelUpdateApp.hideView()
        }
    }

    private fun setPrinterCountText() {
        context?.let {
            val printerPref = PrinterPrefManager(it)
            printerPref.installedPrinters.let { printerList ->
                when {
                    printerList == null -> {
                        binding.tvPrinterCount.text = getString(R.string.empty_printer)
                        binding.tvPrinterCount.setTextColor(ContextCompat.getColor(it, R.color.black_40))
                    }
                    printerList.count() == 1 -> {
                        binding.tvPrinterCount.text = printerList.first().name
                        binding.tvPrinterCount.setTextColor(ContextCompat.getColor(it, R.color.blue_60))
                    }
                    printerList.count() > 1 -> {
                        binding.tvPrinterCount.text = getString(R.string.multuiple_printer, printerList.count())
                        binding.tvPrinterCount.setTextColor(ContextCompat.getColor(it, R.color.blue_60))
                    }
                    else -> {
                        binding.tvPrinterCount.text = getString(R.string.empty_printer)
                        binding.tvPrinterCount.setTextColor(ContextCompat.getColor(it, R.color.black_40))
                    }
                }
            }
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        pagerhandler.removeMessages(0)
    }

    override fun navigate(intent: Intent) {
        startActivity(intent)
    }
}
