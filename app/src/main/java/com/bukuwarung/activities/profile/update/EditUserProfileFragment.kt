package com.bukuwarung.activities.profile.update

import android.Manifest
import android.app.Activity.RESULT_OK
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.provider.MediaStore
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.Toast
import androidx.lifecycle.Observer
import com.airbnb.lottie.LottieAnimationView
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.print.UserProfileMissionSuccessBottomSheet
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.settings.CommonConfirmationBottomSheet
import com.bukuwarung.activities.superclasses.BaseFragment
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AnalyticsConst.ENTRY_POINT2
import com.bukuwarung.constants.AnalyticsConst.STATUS_COMPLETE
import com.bukuwarung.constants.AnalyticsConst.USER_PROFILE_STATUS
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.databinding.FragmentUserProfileEditBinding
import com.bukuwarung.dialogs.businessselector.BusinessSelectorDialog
import com.bukuwarung.dialogs.imageselector.ImageSelectorDialog2
import com.bukuwarung.session.User
import com.bukuwarung.utils.*
import com.bukuwarung.utils.Utilities.isValidEmail
import com.bukuwarung.utils.Utilities.validateDate
import com.bumptech.glide.Glide
import com.bumptech.glide.request.RequestOptions
import java.io.File
import java.text.ParseException
import java.text.SimpleDateFormat
import java.util.*
import javax.inject.Inject

class EditUserProfileFragment : BaseFragment(), UserProfileMissionSuccessBottomSheet.Callback {

    private var fromMission: Boolean = false
    private var userProfileTemp: UserProfileEntity? = null

    @Inject
    lateinit var viewModel: ProfileTabViewModel

    lateinit var binding: FragmentUserProfileEditBinding
    private var bookEntity: BookEntity? = null
    private var businessRepository: BusinessRepository? = null
    var dialog: BusinessSelectorDialog? = null
    private var profilePicUri: Uri? = null
    private var profilePicFile: File? = null
    lateinit var lavSuccessView: LottieAnimationView
    private var successView: View? = null

    companion object {
        val TAG: String = "edit_user_profile_fragment"
        private const val USER_ENTITY = "user_entity"
        private const val FROM_MISSION = "from_mission"

        fun instance(
            userProfileEntity: UserProfileEntity?,
            fromMission: Boolean = false
        ): EditUserProfileFragment {
            val fragment = EditUserProfileFragment()
            val bundle = Bundle()
            bundle.putParcelable(USER_ENTITY, userProfileEntity)
            bundle.putBoolean(FROM_MISSION, fromMission)
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onCreateView(
        layoutInflater: LayoutInflater,
        viewGroup: ViewGroup?,
        bundle: Bundle?
    ): View {

        binding = FragmentUserProfileEditBinding.inflate(layoutInflater, viewGroup, false)
        return binding.root
    }

    override fun onPause() {
        super.onPause()
        binding.spinnerGender.dismiss()
    }

    override fun setupView(view: View) {

        lavSuccessView = view.findViewById(R.id.lav_success)
        successView = view.findViewById(R.id.success_view)


        userProfileTemp = arguments?.getParcelable(USER_ENTITY)
        if (arguments != null) {
            fromMission = arguments?.getBoolean(FROM_MISSION)!!
        }


        viewModel.onEventReceived(ProfileTabViewModel.Event.OnCreateView)

        businessRepository = BusinessRepository.getInstance(Application.getAppContext())
        bookEntity = businessRepository?.getBusinessByIdSync(User.getBusinessId())
        bindLayoutComponents()
        binding.userNameTv.setOnClickListener{
            binding.spinnerGender.dismiss()
        }

        hideGenderSpinner()


        viewModel.getUserProfile(User.getUserId()).observe(this, Observer {

            if (it != null) {

                userProfileTemp = it
                if (it.userName.isNotNullOrEmpty()) {
                    if (!it.userName.equals(getString(R.string.default_owner_name))) {
                        if (binding.userNameTv.text.isNullOrEmpty())
                            binding.userNameTv.setText(userProfileTemp?.userName)
                    }
                }
                binding.userPhoneTv.setText(it.userPhone)

                if (it.userProfileImageUrl.isNotNullOrEmpty())
                    setProfilePic(it.userProfileImageUrl)

                if (it.userEmail != null) {
                    if (binding.userEmailTv.text.isNullOrEmpty())
                        binding.userEmailTv.setText(it.userEmail)
                }

                if (it.dateOfBirth.isNotNullOrEmpty()) {
                    val dateDetails = it.dateOfBirth!!.split("-")
                    val day: String = dateDetails[2]
                    val month: String = dateDetails[1]
                    val year: String = dateDetails[0]

                    if (binding.dayTv.text.isNullOrEmpty())
                        binding.dayTv.setText(day)

                    if (binding.monthTv.text.isNullOrEmpty())
                        binding.monthTv.setText(month)

                    if (binding.yearTv.text.isNullOrEmpty())
                        binding.yearTv.setText(year)
                }

                binding.spinnerGender.text = it.gender
            } else {
                binding.userPhoneTv.setText(userProfileTemp?.userPhone)

                if (userProfileTemp?.userName.isNotNullOrEmpty()) {
                    if (!userProfileTemp?.userName!!.equals(getString(R.string.default_owner_name))) {
                        binding.userNameTv.setText(userProfileTemp?.userName)
                    }
                }

                if (userProfileTemp?.userProfileImageUrl.isNotNullOrEmpty())
                    setProfilePic(userProfileTemp?.userProfileImageUrl)
            }
        })

        binding.backBtn.setOnClickListener {
            binding.spinnerGender.dismiss()
            activity?.let {
                it.supportFragmentManager.executePendingTransactions()
                it.onBackPressed()
            }
        }

    }

    private fun setProfilePic(imageUri: String?) {
        Glide.with(this).load(imageUri)
            .apply(RequestOptions().circleCrop())
            .placeholder(R.drawable.ic_icon_shop)
            .error(R.drawable.ic_icon_shop)
            .into(binding.profilePic)
    }

    fun onClickSave() {
        binding.spinnerGender.dismiss()

        if (!binding.userEmailTv.text.isNullOrEmpty()) {
            if (isValidEmail(binding.userEmailTv.text.toString())) {
                userProfileTemp?.userEmail = binding.userEmailTv.text.toString()
            } else {
                Toast.makeText(context, getString(R.string.check_email), Toast.LENGTH_SHORT).show()
                return
            }
        } else {
            userProfileTemp?.userEmail = null
        }



        if (binding.dayTv.text.isNullOrEmpty() &&
            binding.monthTv.text.isNullOrEmpty() &&
            binding.yearTv.text.isNullOrEmpty()
        ) {
            userProfileTemp?.dateOfBirth = null
        } else if (!binding.dayTv.text.isNullOrEmpty() &&
            !binding.monthTv.text.isNullOrEmpty() &&
            !binding.yearTv.text.isNullOrEmpty()
        ) {
            val year = binding.yearTv.text.toString()
            val day = if (binding.dayTv.text.toString()
                    .toInt() < 10 && binding.monthTv.text.toString().length == 1
            ) "0" + binding.dayTv.text.toString() else binding.dayTv.text.toString()
            val month = if (binding.monthTv.text.toString()
                    .toInt() < 10 && binding.monthTv.text.toString().length == 1
            ) "0" + binding.monthTv.text.toString() else binding.monthTv.text.toString()

            val strDateOfBirth = year + "-" + month + "-" + day

            if (validateDate(strDateOfBirth)) {
                val formatter = SimpleDateFormat("yyyy-MM-dd")
                var d: Date? = null
                try {
                    d = formatter.parse(strDateOfBirth) //catch exception
                    userProfileTemp?.dateOfBirth = strDateOfBirth

                } catch (e: ParseException) {
                    Toast.makeText(
                        context,
                        getString(R.string.check_date_of_birth),
                        Toast.LENGTH_SHORT
                    ).show()
                    e.printStackTrace()
                    return
                }
            } else {
                Toast.makeText(context, getString(R.string.check_date_of_birth), Toast.LENGTH_SHORT)
                    .show()
                return
            }
        } else {
            Toast.makeText(context, getString(R.string.check_date_of_birth), Toast.LENGTH_SHORT)
                .show()
            return
        }

        if (binding.userNameTv.text.toString().isEmpty()) {
            Toast.makeText(context, getString(R.string.business_owner_hint), Toast.LENGTH_SHORT)
                .show()
            return
        } else {
            userProfileTemp?.userName = binding.userNameTv.text.toString()
        }

        if (!binding.spinnerGender.text.equals(getText(R.string.jenis_kelamin))) {
            userProfileTemp?.gender = binding.spinnerGender.text.toString()
        } else ""

        if (userProfileTemp != null) {
            Utilities.sendEventsToBackendWithBureau("business_profile_save", "profile_edit")

            viewModel.setUserProfile(userProfileTemp!!)
        }

        if (fromMission) {
            Thread(SaveProfileThread(this, userProfileTemp)).start()
            UserProfileMissionSuccessBottomSheet.createInstance(false, "").show(
                childFragmentManager,
                UserProfileMissionSuccessBottomSheet.TAG
            )
        } else {
            this.lavSuccessView.showForOnce(this.successView, 75) {
                Thread(SaveProfileThread(this, userProfileTemp)).start()
                binding.backBtn.performClick()
            }
        }

    }

    private fun bindLayoutComponents() {
        binding.save.setOnClickListener {
            binding.spinnerGender.dismiss()
            if (Utility.hasInternet()) {
                onClickSave()
            } else {
                CommonConfirmationBottomSheet().showNoInternetBottomSheet(
                    requireContext(),
                    requireActivity().supportFragmentManager
                )
            }
        }

        binding.editImageIcon.setOnClickListener {
            binding.spinnerGender.dismiss()
            showImageSelectorDialog() }
        binding.profilePic.setOnClickListener {
            binding.spinnerGender.dismiss()
            showImageSelectorDialog() }


    }

    private fun showImageSelectorDialog() {
        if (context == null) return
        val imageSelectorDialog = ImageSelectorDialog2(requireContext(), {
            setImageSourceFromCamera()
        }, {
            getImageFromGallery()
        })
        imageSelectorDialog.window?.setBackgroundDrawableResource(R.drawable.round_corner_white_picture_picker)
        AppAnalytics.trackEvent(AnalyticsConst.EVENT_EDIT_PROFILE_PIC)
        imageSelectorDialog.show()
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        activity?.window?.setSoftInputMode(WindowManager.LayoutParams.SOFT_INPUT_ADJUST_PAN or WindowManager.LayoutParams.SOFT_INPUT_STATE_HIDDEN)
    }

    private fun getImageFromGallery() {
        if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.READ_EXTERNAL_STORAGE) != PackageManager.PERMISSION_GRANTED) {
            requestPermissions(
                PermissionConst.WRITE_EXTERNAL_STORAGE_PERMISSION_STR,
                PermissionConst.REQ_PICK_IMAGE_PERMISSON
            )
            return
        }

        val intent = Intent(Intent.ACTION_OPEN_DOCUMENT).apply {
            type = "image/*"
            addCategory(Intent.CATEGORY_OPENABLE)
        }
        startActivityForResult(
            Intent.createChooser(
                intent,
                getString(R.string.select_image_instruction)
            ), PermissionConst.REQ_PICK_IMAGE_PERMISSON
        )
    }

    private fun setImageSourceFromCamera() {
        try {
            profilePicFile = ImageUtils.createTempFile()
            profilePicUri = ImageUtils.getUriFromFile(profilePicFile)
            val intent = Intent("android.media.action.IMAGE_CAPTURE")
            intent.putExtra("output", profilePicUri)
            intent.addFlags(Intent.FLAG_EXCLUDE_STOPPED_PACKAGES)
            if (Build.VERSION.SDK_INT >= 23 && activity?.checkSelfPermission(Manifest.permission.CAMERA) != PackageManager.PERMISSION_GRANTED) {
                requestPermissions(
                    PermissionConst.CAMER_AND_STORAGE,
                    PermissionConst.REQ_TAKE_PICTURE_PERMISSON
                )
            }
            if (intent.resolveActivity(requireActivity().packageManager) != null) {
                startActivityForResult(intent, PermissionConst.TAKE_PHOTO)
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        when (requestCode) {
            PermissionConst.REQ_TAKE_PICTURE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    setImageSourceFromCamera()
                }
            }
            PermissionConst.REQ_PICK_IMAGE_PERMISSON -> {
                if (grantResults[0] == PackageManager.PERMISSION_GRANTED) {
                    getImageFromGallery()
                }
            }


        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, intent: Intent?) {
        try {
            var uri: Uri? = null
            if (resultCode == RESULT_OK && requestCode == PermissionConst.TAKE_PHOTO) {
                uri = profilePicUri
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_SET_USER_PROFILE_PIC_CAMERA)
            } else if (requestCode == PermissionConst.REQ_PICK_IMAGE_PERMISSON && resultCode == RESULT_OK && intent != null && intent.data != null) {
                uri = intent.data
            }

            uri?.let {
                var bitmap = MediaStore.Images.Media.getBitmap(
                    requireActivity().contentResolver,
                    uri
                )

                bitmap = Utility.fixImageRotation(requireContext(), bitmap, profilePicFile, it)
                profilePicFile = null

                viewModel.onEventReceived(
                    ProfileTabViewModel.Event.OnUploadUserProfileImage(
                        bitmap,
                        uri
                    )
                )
            }
        } catch (e: Exception) {
            e.recordException()
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is ProfileTabViewModel.State.ContinueUploadImage -> setProfilePic(it.uri.toString())
                is ProfileTabViewModel.State.setProfileUrlToUserEntity -> saveUserEntityWithNewProfile(
                    it.url
                )
            }
        }
    }

    private fun saveUserEntityWithNewProfile(url: String) {
        userProfileTemp?.userProfileImageUrl = url
        if (userProfileTemp != null) {
            viewModel.setUserProfile(userProfileTemp!!)
        }
    }

    private fun hideGenderSpinner()
    {
        binding.apply{
            this.userNameTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }
            this.userEmailTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }

            this.userPhoneTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }
            this.dayTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }
            this.monthTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }
            this.yearTv.setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus)
                    binding.spinnerGender.dismiss()
            }
            this.profileLayout.setOnClickListener {
                binding.spinnerGender.dismiss()
            }
        }
    }

    override fun seeNote(entryPoint: String) {
        activity?.finish()
    }
}