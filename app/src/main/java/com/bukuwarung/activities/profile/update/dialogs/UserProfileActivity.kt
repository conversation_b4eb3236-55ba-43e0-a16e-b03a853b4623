package com.bukuwarung.activities.profile.update.dialogs

import android.content.Intent
import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.lifecycle.observe
import com.bukuwarung.R
import com.bukuwarung.activities.home.MainActivity
import com.bukuwarung.activities.home.TabName
import com.bukuwarung.activities.home.constants.MainActivityConstants
import com.bukuwarung.activities.profile.ProfileTabFragment.Companion.SHOW_EDIT_PROFILE
import com.bukuwarung.activities.profile.ProfileTabFragment.Companion.FROM_MISSION
import com.bukuwarung.activities.profile.ProfileTabFragment.Companion.USER_PROFILE_TEMP
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.profile.update.EditUserProfileFragment
import com.bukuwarung.activities.profile.update.UserProfileDetailsFragment
import com.bukuwarung.activities.superclasses.BaseActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.databinding.ActivityUserProfileNewBinding
import com.bukuwarung.payments.addbank.AddBankAccountActivity
import com.bukuwarung.payments.banklist.BankAccountListActivity
import com.bukuwarung.payments.banklist.BankAccountListViewModel
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.utils.subscribeSingleLiveEvent
import javax.inject.Inject

class UserProfileActivity : BaseActivity() {

    private var fromMission  = false
    private lateinit var listBankAccounts: List<BankAccount>

    @Inject
    lateinit var viewModel: ProfileTabViewModel
    @Inject
    lateinit var bankAccountListViewModel: BankAccountListViewModel
    private lateinit var binding: ActivityUserProfileNewBinding
    private var userProfileTemp: UserProfileEntity? = null
    private var showShowEditUserProfile = false


    private val bookId by lazy { viewModel.getCurrentBusiness()?.bookId }
    private val customerId by lazy { null }
    private val selectedAccountId by lazy { null }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        if (intent.getStringExtra(MainActivityConstants.TAB_NAME) != null) {
            val tabNameString = intent.getStringExtra(MainActivityConstants.TAB_NAME)
            navigateTabFromIntentExtra(
                tabNameString
            )
        }
    }

    private fun navigateTabFromIntentExtra(tabNameString: String?) {
        if (tabNameString != null) {
            try {
                val tabName = TabName.valueOf(tabNameString)
            } catch (ignored: Exception) {
                //do nothing
            }
        }
    }


    override fun setViewBinding() {
        binding = ActivityUserProfileNewBinding.inflate(layoutInflater)
        setContentView(binding.root)
    }

    override fun setupView() {
        userProfileTemp = intent.getParcelableExtra(USER_PROFILE_TEMP)
        fromMission = intent.getBooleanExtra(FROM_MISSION,false)
        showShowEditUserProfile = intent.getBooleanExtra(SHOW_EDIT_PROFILE, false)
        bankAccountListViewModel.init(
            PaymentConst.TYPE_PAYMENT_IN,
            AnalyticsConst.LAINNYA_DIRECT_EXISTING,
            customerId,
            bookId,
            selectedAccountId
        )
        if (showShowEditUserProfile) {
            supportFragmentManager.beginTransaction()
                .add(R.id.main_container, EditUserProfileFragment.instance(userProfileTemp))
                .commit()
        }
        else if (fromMission){
            supportFragmentManager.beginTransaction().add(R.id.main_container,
                EditUserProfileFragment.instance(userProfileTemp,fromMission)).commit()
        }
        else {
            supportFragmentManager.beginTransaction().add(
                R.id.main_container,
                UserProfileDetailsFragment.instance(userProfileTemp)
            ).commit()
        }



        supportFragmentManager.addOnBackStackChangedListener {
            val currentFragment: Fragment? = supportFragmentManager.findFragmentById(R.id.main_container)
            if (currentFragment is UserProfileDetailsFragment) {
                currentFragment.userProfileOptionsAdapter.notifyDataSetChanged()
            }
        }
    }

    override fun subscribeState() {
        subscribeSingleLiveEvent(viewModel.state) {
            when (it) {
                is ProfileTabViewModel.State.GoToRegisterBankAccount -> goToRegisterBankAccount()
            }
        }

        bankAccountListViewModel.mediatorLiveData.observe(this) {
            listBankAccounts = it
        }
    }

    private fun goToRegisterBankAccount() {
        if (PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_ADD_BANK_ACCOUNT)) {
            PaymentUtils.showKycKybStatusBottomSheet(supportFragmentManager, AnalyticsConst.LAINNYA)
            return
        }

        val i : Intent
        if (listBankAccounts.isEmpty()) {
            i = AddBankAccountActivity.createIntent(
                this,
                PaymentConst.TYPE_PAYMENT_IN.toString(),
                bookId,
                AnalyticsConst.LAINNYA_DIRECT_FIRST,
                hasBankAccount = "false"
            )
        } else {
            AppConfigManager.getInstance().setBankLayoutBaru()
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_OPEN_USER_BANK_LIST)
            i = BankAccountListActivity.createIntent(
                this,
                PaymentConst.TYPE_PAYMENT_IN.toString(),
                bookId,
                AnalyticsConst.LAINNYA_DIRECT_EXISTING,
                isSelfOnly = "true"
            )
        }
        startActivity(i)
    }
}