package com.bukuwarung.activities.referral.payment_referral

import android.app.Activity
import android.app.PendingIntent
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.View
import android.view.inputmethod.EditorInfo
import android.widget.EditText
import android.widget.Toast
import androidx.core.app.ActivityCompat
import androidx.core.content.ContextCompat
import androidx.core.widget.doOnTextChanged
import androidx.lifecycle.Observer
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.bukuwarung.R
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.activities.profile.ProfileTabViewModel
import com.bukuwarung.activities.referral.main_referral.MainReferralActivity
import com.bukuwarung.activities.referral.main_referral.MainReferralPresenter
import com.bukuwarung.activities.referral.share.ReferralUploadReceiver
import com.bukuwarung.activities.superclasses.AppActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.analytics.SurvicateAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PermissionConst
import com.bukuwarung.contact.ui.ContactSearchViewModel
import com.bukuwarung.contact.ui.ReferralContactAdapter
import com.bukuwarung.controllers.firebase.FirebaseDynamicLinksController
import com.bukuwarung.data.referral.ReferralContent
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.database.entity.referral.PaymentUserReferral
import com.bukuwarung.database.repository.ReferralRepository
import com.bukuwarung.database.repository.TransactionRepository
import com.bukuwarung.dialogs.base.BaseDialog
import com.bukuwarung.dialogs.contact.ContactPermissionBottomSheetDialog
import com.bukuwarung.dialogs.loading.LoadingDialog
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.preference.ReferralPrefManager
import com.bukuwarung.referral.model.ReferralDataPostRequestPayload
import com.bukuwarung.referral.model.ReferralDataPutRequest
import com.bukuwarung.referral.model.ReferralDataResponsePayload
import com.bukuwarung.referral.usecase.ReferralUseCase
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.share.ShareLayoutImage
import com.bukuwarung.utils.*
import com.bumptech.glide.Glide
import com.google.android.gms.tasks.Task
import com.google.android.gms.tasks.TaskExecutors
import dagger.android.AndroidInjection
import kotlinx.android.synthetic.main.activity_referral.*
import kotlinx.coroutines.launch
import javax.inject.Inject

class ReferralActivity : AppActivity(),
    ReferralRepository.OnLeaderBoardUpdateNameCallback,
    ContactPermissionBottomSheetDialog.ContactPermissionSheetListener {

    private lateinit var dynamicLinksController: FirebaseDynamicLinksController
    private lateinit var data: ReferralDataResponsePayload

    private lateinit var paymentUserReferralData: PaymentUserReferral
    private lateinit var progressDialog: ProgressDialog
    private val isContactListEnabled: Boolean = RemoteConfigUtils.Referral.isContacListEnabled()
    private var hasFireSearchEvent: Boolean = false
    private var from: String? = null
    private var username: String = ""
    private lateinit var referralContents: ReferralContent

    companion object {
        var refCode: String? = null
    }

    @Inject
    lateinit var referralUseCase: ReferralUseCase

    @Inject
    lateinit var viewModel: ContactSearchViewModel
    @Inject
    lateinit var viewModelProfile: ProfileTabViewModel
    lateinit var editText: EditText

    private val referralContactAdapter: ReferralContactAdapter by lazy {
        ReferralContactAdapter {
            Log.d(this::class.java.name, it.toString())
            shareReferralCodeNew(it.mobile, true)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        AndroidInjection.inject(this)
        setContentView(R.layout.activity_referral)
        setupView()
    }

    private fun setupView() {
        // code after clicking on invite friends

        referralContents = RemoteConfigUtils.getReferralContents()

        val referralPageContent = referralContents.referral_page_content

        with(referralPageContent) {
            screen_title.text = title
            Glide.with(this@ReferralActivity).load(referral_image).placeholder(R.drawable.referral_illustration).into(referralImage)
            referral_header_txt.text = referral_share_text
            referral_subtxt.text = referral_subtext
            tv_tnc.text = referral_bonus_text
            inviteFriendsBtn.text = referral_share_button_text
            tv_invited.text = referral_instruction_text
            btn_allow_contact_access.text = referral_contact_buttton_text
            invite_friends_faster.text = referral_contact_info
            Glide.with(this@ReferralActivity).load(referral_contact_image).placeholder(R.drawable.ic_phonebook_new).into(img_phone_book)
        }

        inviteFriendsBtn.setOnClickListener {
            if (Utility.hasInternet()) {
                shareReferralCodeNew()
            } else {
                Toast.makeText(this, getString(R.string.no_internet_connection), Toast.LENGTH_LONG)
                    .show()
            }
        }

        progressDialog =
            ComponentUtil.getProgressDialog(this, getString(R.string.please_wait), false)
        backBtn.setOnClickListener { onBackPressed() }

        tv_invited.setOnClickListener { openReferralWebView(WebviewActivity.REFERRAL_TAB_REFERRED) }


        group_phonebook.visibility =
            (!PermissonUtil.hasContactPermission() && isContactListEnabled).asVisibility()
        group_contact_list.visibility =
            (PermissonUtil.hasContactPermission() && isContactListEnabled).asVisibility()
        invite_friends_faster.visibility = (!PermissonUtil.hasContactPermission()).asVisibility()

        btn_allow_contact_access.setOnClickListener {
            val dialog = ContactPermissionBottomSheetDialog.newInstance(
                AnalyticsConst.REFERRAL_HOME_PAGE
            )
            dialog.show(supportFragmentManager, ContactPermissionBottomSheetDialog.TAG)
            AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFERRAL_CONTACT_PERMISSION_BUTTON_CLICK)
        }

        if (intent.hasExtra("from")) {
            from = intent.getStringExtra("from")
        }

        et_search_contact.addTextChangedListener(object : TextWatcher {
            override fun afterTextChanged(s: Editable?) {
                if (!hasFireSearchEvent) {
                    val prop = AppAnalytics.PropBuilder()
                        .put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.REFERRAL_HOME_PAGE)
                    AppAnalytics.trackEvent(
                        AnalyticsConst.EVENT_SEARCH_CONTACT_WITH_PHONEBOOK,
                        prop
                    )
                    hasFireSearchEvent = true
                }
                viewModel.onSearchQueryChangeForReferral(s.toString())
            }

            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {
            }

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            }
        })

        rv_contact.apply {
            adapter = referralContactAdapter
            layoutManager = LinearLayoutManager(this@ReferralActivity)
        }
        subscribeViewModelEvent()
        if (PermissonUtil.hasContactPermission()) {
            viewModel.loadDeviceContactOnly()
        }

        et_search_contact.apply {
            setOnFocusChangeListener { _, hasFocus ->
                if (hasFocus) {
                    content_scrollView.scrollTo(0, tv_successful_referral_message.top)
                }
            }

            setOnEditorActionListener { _, actionId, _ ->
                Utility.hideKeyboard(this@ReferralActivity)
                if (actionId == EditorInfo.IME_ACTION_DONE) {
                    clearFocus()
                    true
                } else {
                    false
                }
            }
        }
        val referralFloatingButtonVisibilityThreshold =
            RemoteConfigUtils.ReferralFloatingFeature.floatingButtonVisibilityThreshold()
        val isReferralFloatingButtonVisible =
            RemoteConfigUtils.ReferralFloatingFeature.isFloatingButtonEnabled()

        val isTvInvitedVisible = RemoteConfigUtils.showTvInvited.isTvInvitedButtonEnabled()

        tv_info.setOnClickListener {
            TnCBottomSheet().apply {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_READ_REFERRAL_TERMS_AND_CONDITION)
                show(supportFragmentManager, TnCBottomSheet.TAG)
            }
        }

        tv_tnc.setOnClickListener {
            TnCBottomSheet().apply {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_READ_REFERRAL_TERMS_AND_CONDITION)
                show(supportFragmentManager, TnCBottomSheet.TAG)
            }
        }

        val txnCount =
            TransactionRepository.getInstance(this).transactionCountWithDeletedRecords + FeaturePrefManager.getInstance().paymentTransactionCount

        tv_info.visibility = isReferralFloatingButtonVisible.asVisibility()
        ib_info.visibility = isReferralFloatingButtonVisible.asVisibility()
        tv_invited.visibility = isTvInvitedVisible.asVisibility()

        cl_referral.visibility =
            (txnCount == 0 && !ReferralPrefManager.getInstance().isRefCodeUsed).asVisibility()

        var refCode = ReferralPrefManager.getInstance().temporaryReferralCode
        tv_successful_referral_message.visibility = View.GONE
        if (refCode.isNotNullOrEmpty()) {
            enterReferral.setText(refCode)
            enterReferral.isEnabled = false
            confirmationBtn.isEnabled = true
            confirmationBtn.setBackgroundColor(
                ContextCompat.getColor(
                    this,
                    R.color.buku_CTA_New
                )
            )
        } else {
            enterReferral.isEnabled = true
        }

        enterReferral.doOnTextChanged { text, start, before, count ->
            text?.let {
                confirmationBtn.isEnabled = text.length >= 4
                if (confirmationBtn.isEnabled) {
                    confirmationBtn.setBackgroundColor(
                        ContextCompat.getColor(
                            this,
                            R.color.buku_CTA_New
                        )
                    )
                } else {
                    confirmationBtn.setBackgroundColor(ContextCompat.getColor(this, R.color.grey))
                }
                refCode = text.toString()

                if (text.length == 0) {
                    enterReferral.setBackgroundResource(R.drawable.bg_edittext_default)
                } else {
                    enterReferral.setBackgroundResource(R.drawable.bg_white_edittext_blue_outline)
                }

            }
        }

        // code after clicking on "Konfirmasi" button
        confirmationBtn.setSingleClickListener {
            if (ReferralPrefManager.getInstance().paymentReferralInUse.isEmpty()) {
                val referredByCode: String = enterReferral.text.toString()

                if (referredByCode.isNotNullOrEmpty() && referredByCode != ReferralPrefManager.getInstance().myReferalCode) {
                    // Register manual referral code event
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(AnalyticsConst.ENTRY_POINT2,AnalyticsConst.REFERRAL_HOME_PAGE)
                    prop.put("referral_code_input_method", AnalyticsConst.MANUAL)
                    prop.put(AnalyticsConst.ENTERED_REFERRAL_CODE, referredByCode)
                    prop.put("referral_code_input_status", AnalyticsConst.CODE_ACCEPTED)
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_REFERRAL_CODE, prop)

                    var obj = ReferralDataPutRequest(referredByCode)
                    var putResponse: ReferralDataResponsePayload?

                    lifecycleScope.launch {

                        putResponse = referralUseCase.putReferralData(obj)

                        if (putResponse != null) {
                            AppAnalytics.trackEvent("Referral_activity_send_referral_code_to_server_success")
                            ReferralPrefManager.getInstance().paymentReferralInUse =
                                putResponse!!.referredByCode
                            ReferralPrefManager.getInstance().referralDeeplink =
                                putResponse!!.userReferralDeeplink
                            runOnUiThread {
                                enterReferral.visibility = View.GONE
                                confirmationBtn.visibility = View.GONE
                                Toast.makeText(
                                    this@ReferralActivity,
                                    getString(R.string.referral_successful),
                                    Toast.LENGTH_LONG
                                ).show()



                            }
                        } else {
                            AppAnalytics.trackEvent("Referral_activity_send_referral_code_to_server_failure")
                            runOnUiThread {
                                enterReferral.setBackgroundResource(R.drawable.bg_white_edittext_red_outline)
                                Toast.makeText(
                                    this@ReferralActivity,
                                    getString(R.string.invalid_referral_code),
                                    Toast.LENGTH_LONG
                                ).show()
                            }

                        }
                        val prop2 = AppAnalytics.PropBuilder()
                        prop2.put(AnalyticsConst.ACTIVATION_TYPE, AnalyticsConst.MANUAL_INPUT)
                        prop2.put(AnalyticsConst.STATUS, if (putResponse != null) AnalyticsConst.STATUS_SUCCESS else AnalyticsConst.STATUS_FAIL)
                        prop2.put(AnalyticsConst.REFERRER_CODE, referredByCode)
                        AppAnalytics.trackEvent(AnalyticsConst.EVENT_REFERRAL_ACTIVATION_RESPONSE,prop2)
                    }
                } else {
                    // Register manual referral code event
                    val prop = AppAnalytics.PropBuilder()
                    prop.put(AnalyticsConst.ENTRY_POINT2,AnalyticsConst.REFERRAL_HOME_PAGE)
                    prop.put("referral_code_input_method", AnalyticsConst.MANUAL)
                    prop.put(AnalyticsConst.ENTERED_REFERRAL_CODE, referredByCode)
                    prop.put("referral_code_input_status", AnalyticsConst.CODE_NOT_ACCEPTED)
                    AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_REFERRAL_CODE, prop)

                    Toast.makeText(
                        this@ReferralActivity,
                        getString(R.string.invalid_referral_code),
                        Toast.LENGTH_LONG
                    ).show()
                }
            }else{
                // Register manual referral code event
                val referredByCode: String = enterReferral.text.toString()
                val prop = AppAnalytics.PropBuilder()
                prop.put(AnalyticsConst.ENTRY_POINT2,AnalyticsConst.REFERRAL_HOME_PAGE)
                prop.put("referral_code_input_method", AnalyticsConst.MANUAL)
                prop.put(AnalyticsConst.ENTERED_REFERRAL_CODE, referredByCode)
                prop.put("referral_code_input_status", AnalyticsConst.CODE_NOT_ACCEPTED)
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_INPUT_REFERRAL_CODE, prop)
            }
        }

        AppAnalytics.setUserProperty(
            AnalyticsConst.IS_PHONEBOOK_ENABLED,
            isContactListEnabled.toString()
        )
        AppAnalytics.setUserProperty(
            AnalyticsConst.IS_PHONEBOOK_ENABLED,
            isContactListEnabled.toString()
        )

        lifecycleScope.launch {
            val reffCode: String = ReferralPrefManager.getInstance().myReferalCode
            if (reffCode.isNotEmpty()) {
                runOnUiThread {
                    referralCodeMessage.text = reffCode
                    progressDialog.dismiss()
                }
                if (ReferralPrefManager.getInstance().paymentReferralInUse.isNotEmpty()) {
                    runOnUiThread {
                        enterReferral.visibility = View.GONE
                        confirmationBtn.visibility = View.GONE
                    }
                }
                referralCodeMessage.setDrawableRightListener {
                    Utility.copyToClipboard(reffCode, this@ReferralActivity)
                }
            } else {
                val getResponse = referralUseCase.getReferralData()

                if (getResponse != null) {
                    onUserNewReferralDataLoaded(getResponse)
                    Log.d("reached", getResponse.toString())
                } else {
                    var obj = ReferralDataPostRequestPayload("")
                    val postResponse = referralUseCase.postReferralData(obj)
                    onUserNewReferralDataLoaded(postResponse)
                    Log.d("reached", postResponse.toString())
                }

                if (ReferralPrefManager.getInstance().paymentReferralInUse.isNotEmpty()) {
                    runOnUiThread {
                        enterReferral.visibility = View.GONE
                        confirmationBtn.visibility = View.GONE
                    }
                }
            }

        }

        viewModelProfile.getUserProfile(User.getUserId()).observe(this, Observer {
            if (it == null) {
                if (SessionManager.getInstance()
                        .isGuestUser()
                ) {
                    username = ""
                } else {
                    val bookEntity: BookEntity = viewModelProfile.getBusinessByIdSync(User.getBusinessId())
                    val userProfileTemp = UserProfileEntity(
                        User.getUserId(),
                        if (bookEntity.hasCompletedProfileWithOwnerName()) bookEntity.businessOwnerName else getString(
                            R.string.default_owner_name
                        ),
                        User.getUserId()
                    )
                    if (userProfileTemp.userName.isNullOrEmpty()) {
                        username = ""
                    } else {
                        username = userProfileTemp.userName!!
                    }
                }
            } else {
                if (it.userName.isNullOrEmpty()) {
                    username = ""
                } else {
                    username = it.userName!!
                }
            }
        })

    }

    private fun subscribeViewModelEvent() {
        viewModel.contactsObserver.observe(this, Observer {
            when (it) {
                is ContactSearchViewModel.ContactEvent.DeviceContactsLoaded -> {
                    referralContactAdapter.setContactData(it.contacts)
                }
            }
        })
    }

    /** Sets the referral code and shared preference keys with their respective values */
    fun onUserNewReferralDataLoaded(referralDataPayload: ReferralDataResponsePayload?) {
        val referralString = referralDataPayload?.userReferralCode
        runOnUiThread {
            referralCodeMessage.text = referralString
            progressDialog.dismiss()
        }
        referralDataPayload?.let {
            data = it
            ReferralPrefManager.getInstance().myReferalCode = data.userReferralCode
            ReferralPrefManager.getInstance().paymentReferralInUse = data.referredByCode
            ReferralPrefManager.getInstance().referralDeeplink = data.userReferralDeeplink
            return
        }
    }


    private fun shareReferralCodeNew(phoneNumber: String? = null, isPhonebook: Boolean? = false) {
        try {
            val dialog = openLoadingDialog()
            if (ReferralPrefManager.getInstance().referralDeeplink.isEmpty()) {
                lifecycleScope.launch {
                    val response = referralUseCase.getReferralData()

                    if (response != null) {
                        ReferralPrefManager.getInstance().referralDeeplink =
                            response.userReferralDeeplink
                    }
                }
            }

            val referralLink = ReferralPrefManager.getInstance().referralDeeplink

            if (referralLink.isNotEmpty()) {
                dialog.dismiss()
                Log.d(MainReferralPresenter.TAG, "Got Referral Link $referralLink")
                val propBuilder = AppAnalytics.PropBuilder()
                propBuilder.put("referral_link", referralLink)
                propBuilder.put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.REFERRAL_HOME_PAGE)
                if (isPhonebook == true) {
                    propBuilder.put(AnalyticsConst.ENTRY_POINT, "contact")
                }

                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_REFERRAL_SHARE_LINK_SUCCESS,
                    propBuilder
                )
                SurvicateAnalytics.invokeEventTracker(AnalyticsConst.EVENT_REFERRAL_SHARE_LINK_SUCCESS, this)
                openShareChooser(referralLink, phoneNumber, isPhonebook)
            }
        } catch (ex: Exception) {
            ex.printStackTrace()
        }
    }

    fun openLoadingDialog(): BaseDialog {
        val dialog = LoadingDialog(this)
        dialog.show()
        return dialog
    }



    fun openShareChooser(referralLink: String, mobile: String?, isPhonebook: Boolean? = false) {
        val referralMessage = (referralContents.referral_share_content.referral_share_text_new
            ?.replace("[link]", referralLink) ?: "-").replace("[name]", if (username.isNotNullOrEmpty())" "+username else "")
        val referralFloatingButtonRedirectionType =
            RemoteConfigUtils.ReferralFloatingFeature.floatingButtonRedirectionType()
        if (referralFloatingButtonRedirectionType.equals(AppConst.LEADERBOARD_STR)) {
            Glide.with(this@ReferralActivity).load(referralContents.referral_share_content.referral_share_image).placeholder(R.drawable.wa_share_referral).into(referralImagePreview)
        }

        generateAndShareViewImage(
            this,
            referralImagePreview,
            referralMessage.replace("[input_referral_code]", ReferralPrefManager.getInstance().myReferalCode) ?: "-",
            mobile,
            isPhonebook
        )
    }

    fun alertShareError() {
        NotificationUtils.alertToast(resources.getString(R.string.sorry_share_referral))
    }

    fun alertOwnerNameNull() {

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        when (requestCode) {
            MainReferralActivity.ACTIVITY_FILL_PROFILE_ID -> {
                if (resultCode == Activity.RESULT_OK) {
                    shareReferralCodeNew()
                }
            }
        }
    }

    private fun generateAndShareViewImage(
        context: Context?,
        layout: View?,
        text: String?,
        mobile: String? = null,
        isPhonebook: Boolean? = false
    ) {
        try {
            val saveViewSnapshot: Task<ImageUtils.SaveToDiskTaskResult> =
                ImageUtils.saveLayoutConvertedImage(layout, false)
            val receiverIntent = Intent(this, ReferralUploadReceiver::class.java)

            if (from.isNotNullOrEmpty()) {
                receiverIntent.putExtra("from", from)
            }

            if (isPhonebook == true) {
                receiverIntent.putExtra(AnalyticsConst.ENTRY_POINT, "referral_phonebook")
            } else {
                receiverIntent.putExtra(
                    AnalyticsConst.ENTRY_POINT,
                    AnalyticsConst.REFERRAL_HOME_PAGE
                )
            }
            val pendingIntent = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE)
            } else {
                PendingIntent.getBroadcast(this, 0, receiverIntent, PendingIntent.FLAG_UPDATE_CURRENT)
            }
            val shareLayoutImage = ShareLayoutImage(
                text,
                context,
                if (mobile.isNotNullOrEmpty()) "com.whatsapp" else "",
                mobile ?: "",
                mobile.isNotNullOrEmpty(),
                "Bagikan Dengan",
                true,
                pendingIntent,
                true
            )
            saveViewSnapshot.continueWith(TaskExecutors.MAIN_THREAD, shareLayoutImage)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    override fun onLeaderBoardUpdateNameSuccess() {

    }

    private fun openReferralWebView(tabIndex: Int? = null) {
        val url = RemoteConfigUtils.getReferralUrl()
        val webViewIntent =
            WebviewActivity.createIntent(
                this,
                url,
                referralContents.referral_page_content.referral_instruction_text,
                false,
                "referral",
                "referral"
            ).apply {
                putExtra(WebviewActivity.WEBVIEW_PARAM_USE_REFERRAL, true)
                putExtra(WebviewActivity.WEBVIEW_PARAM_REFERRAL_TAB_INDEX, tabIndex)
            }

        trackOpenReferralEvent(tabIndex)
        startActivity(webViewIntent)
    }

    private fun trackOpenReferralEvent(tabIndex: Int?) {
        val event = when (tabIndex) {
            WebviewActivity.REFERRAL_TAB_REWARD -> AnalyticsConst.EVENT_REWARD_TAB_LOAD
            else -> AnalyticsConst.EVENT_REDIRECT_FROM_PN
        }
        AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.REFERRAL_POINTS_BALANCE, 123) // TODO: 5/25/21
            put(AnalyticsConst.USER_REFERRAL_CODE, ReferralPrefManager.getInstance().myReferalCode)
            put(AnalyticsConst.TOTAL_INVITES, 5) // TODO: 5/25/21
            put(AnalyticsConst.SIGNED_UP, 4) // TODO: 5/25/21
            put(AnalyticsConst.WAITING_TO_SIGN_UP, 3) // TODO: 5/25/21
            put(AnalyticsConst.ENTRY_POINT2, event)
        }.also {
            AppAnalytics.trackEvent(event, it)
        }
    }

    override fun allowPermission() {
        if (!PermissonUtil.hasContactPermission()) {
            ActivityCompat.requestPermissions(
                this,
                PermissionConst.READ_WRITE_CONTACTS,
                PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION
            )
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == PermissionConst.REQ_READ_WRITE_CONTACTS_PERMISSION && Build.VERSION.SDK_INT >= 23) {
            group_phonebook.visibility = (!PermissonUtil.hasContactPermission()).asVisibility()
            group_contact_list.visibility =
                (PermissonUtil.hasContactPermission() && isContactListEnabled).asVisibility()
            invite_friends_faster.visibility =
                (!PermissonUtil.hasContactPermission()).asVisibility()

            if (PermissonUtil.hasContactPermission()) {
                viewModel.loadDeviceContactOnly()
            }
        }
    }

}