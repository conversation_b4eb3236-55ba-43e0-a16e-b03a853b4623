package com.bukuwarung.activities.transaction.customer

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.RECORD_IN_DEBT
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.BookEntity
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CustomerRepository
import com.bukuwarung.database.entity.BankAccount
import com.bukuwarung.payments.data.model.PaymentCollection
import com.bukuwarung.payments.data.model.PaymentExtras
import com.bukuwarung.payments.data.model.PaymentOverviewRequest
import com.bukuwarung.payments.data.repository.PaymentsRepository
import com.bukuwarung.preference.AppConfigManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utility
import com.bukuwarung.utils.orNil
import kotlinx.coroutines.launch
import java.math.BigDecimal

class CustomerTransactionViewModel constructor(
        //TODO: change to use case
        private val paymentUseCase: PaymentsRepository,
        private val customerUseCase: CustomerRepository,
        private val businessUseCase: BusinessRepository
) : BaseViewModel() {

    sealed class Event {
        data class ShowReminderPopup(val isWa: Boolean) : Event()
        data class ShareWithoutPaymentLink(val isWa: Boolean, val balanceAmount: String) : Event()
        data class ShareWithPaymentLink(val isWa: Boolean, val balanceAmount: String, val url: String) : Event()
    }

    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private var selectedBankAccount: BankAccount? = null
    private var bookId: String = SessionManager.getInstance().businessId
    private var bookEntity: BookEntity? = null
    private var adminFee: Double = 0.0
    private var isWa = false

    data class ViewState(
            val showLoading: Boolean = false,
            val adminFee: Double = 0.0
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    private lateinit var customerId: String

    fun checkBankAccounts(isWa: Boolean, customerId: String) {
        eventStatus.value = Event.ShowReminderPopup(isWa)
        this.customerId = customerId
        this.isWa = isWa
        if (selectedBankAccount != null) {
            viewState.value = currentViewState().copy(showLoading = false)
            if (!selectedBankAccount!!.getActiveAccount()) {
                eventStatus.value = Event.ShareWithoutPaymentLink(isWa, "")
            }
            return
        }
        bookEntity = businessUseCase.getBusinessByIdSync(bookId)
        //TODO: also check if bankaccount is not active
        bookEntity?.run {
            if (!this.hasCompletedProfileWithoutOwnerName()) {
                eventStatus.value = Event.ShareWithoutPaymentLink(isWa, "")
            } else getBankAccounts()
        }
    }

    private fun getPaymentInOverview() = viewModelScope.launch {
        customerId?: return@launch
        val customer = customerUseCase.getCustomerById(customerId)
        if (!currentViewState().showLoading) viewState.value = currentViewState().copy(showLoading = true)
        val request = PaymentOverviewRequest(
                amount = BigDecimal(55555), // Hardcoded! because amount is not the defining param for admin fee
                description = "",
                bankAccountId = selectedBankAccount?.bankAccountId,
                accountId = bookId,
                customerId = customerId,
                customerName = customer?.name ?: "",
                note = "",
                bankCode = selectedBankAccount!!.bankCode!!
        )
        when (val result = paymentUseCase.getPaymentOverview(request.accountId, customerId, request)) {
            is ApiSuccessResponse -> {
                adminFee = result.body.fee.orNil - result.body.discountFee.orNil
                viewState.value = currentViewState().copy(adminFee = adminFee, showLoading = false)
            }
            //TODO
//            is ApiErrorResponse -> handleErrorApi(PaymentDetailViewModel.API_PAYMENT_OVERVIEW, result.errorMessage)
//            else -> handleErrorApi(PaymentDetailViewModel.API_PAYMENT_OVERVIEW)
        }
    }

    private fun getBankAccounts() = viewModelScope.launch {
        viewState.value = currentViewState().copy(showLoading = true)
        when (val result = paymentUseCase.getMerchantBankAccounts(bookId)) {
            is ApiSuccessResponse -> {
                val size = result.body?.size ?: 0
                FeaturePrefManager.getInstance().setHasBankAccount(size > 0, bookId)
                if (size > 0) {
                    selectedBankAccount = result.body?.firstOrNull()
                    if (selectedBankAccount != null && !selectedBankAccount!!.getActiveAccount())
                            eventStatus.value = Event.ShareWithoutPaymentLink(isWa, "")
                    getPaymentInOverview().join()
                } else {
                    eventStatus.value = Event.ShareWithoutPaymentLink(isWa, "")
                }
                viewState.value = currentViewState().copy(showLoading = false)
            }
            else -> {
                viewState.value = currentViewState().copy(showLoading = false)
                eventStatus.value = Event.ShareWithoutPaymentLink(isWa, "")
            }
        }
    }

    fun shareInvoice(balanceAmount: String, createPayment: Boolean) {
        if(createPayment) createInDisbursement(balanceAmount)
        else eventStatus.value = Event.ShareWithoutPaymentLink(isWa, balanceAmount)
    }

    fun createInDisbursement(balanceAmount: String) = viewModelScope.launch {
        if (!currentViewState().showLoading) viewState.value = currentViewState().copy(showLoading = true)
        val customer = customerUseCase.getCustomerById(customerId)
        val amount = BigDecimal(Utility.extractAmountFromText(balanceAmount))
        if (amount < BigDecimal(RemoteConfigUtils.getMinimumPaymentAmount())) {
            viewState.value = currentViewState().copy(showLoading = false)
            eventStatus.value = Event.ShareWithoutPaymentLink(isWa, Utility.formatAmount(amount.toDouble()))
            return@launch
        }
        val result = paymentUseCase.requestPayment(
                bookId,
                customerId,
                PaymentCollection.newCollectionRequest(
                        amount = amount.toLong(),
                        bankAccountId = selectedBankAccount!!.bankAccountId,
                        description = "-",
                        customerName = customer?.name,
                        extras = PaymentExtras(source = PaymentConst.MANUAL_REMINDER, recordIn = RECORD_IN_DEBT)
                )
        )

        when (result) {
            is ApiSuccessResponse -> {
                AppAnalytics.trackEvent(AnalyticsConst.EVENT_PAYMENT_REQUEST_CREATED,
                        AppAnalytics.PropBuilder()
                                .put("entry_point", AnalyticsConst.MANUAL_REMINDER)
                                .put("inwards_amount", amount)
                                .put("bank", selectedBankAccount?.bankCode)
                )
                viewState.value = currentViewState().copy(showLoading = false)
                eventStatus.value = Event.ShareWithPaymentLink(isWa, balanceAmount,
                        result.body.invoiceUrl ?: "")
                AppConfigManager.getInstance().sethasPendingPayment(true)
            }
            else -> {
                viewState.value = currentViewState().copy(showLoading = false)
                eventStatus.value = Event.ShareWithoutPaymentLink(isWa, balanceAmount)
            }
        }
    }
}
