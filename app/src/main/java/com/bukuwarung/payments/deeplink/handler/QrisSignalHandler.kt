package com.bukuwarung.payments.deeplink.handler

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import androidx.fragment.app.FragmentManager
import com.bukuwarung.activities.WebviewActivity
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base.neuro.api.BukuWarungSignalHandler
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.dialogs.update.UpdateBottomSheet
import com.bukuwarung.domain.payments.BankingUseCase
import com.bukuwarung.neuro.api.Navigator
import com.bukuwarung.neuro.api.Signal
import com.bukuwarung.payments.constants.NameMatchingStatus
import com.bukuwarung.payments.constants.QrisAndKycStatus
import com.bukuwarung.payments.constants.VerificationStatus
import com.bukuwarung.payments.data.model.QrisInfoSubset
import com.bukuwarung.payments.data.model.WhitelistGroupStatus
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.payments.qris.QrisActivity
import com.bukuwarung.payments.utils.PaymentUtils
import com.bukuwarung.payments.widget.SwitchToQrisBookBottomSheet
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.RemoteConfigUtils
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.isTrue
import com.google.firebase.crashlytics.FirebaseCrashlytics
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import javax.inject.Inject


class QrisSignalHandler @Inject constructor(
    private val bankingUseCase: BankingUseCase
) : BukuWarungSignalHandler() {

    companion object {
        const val qrisPath = "/qris"
        private const val qrisLink = "${AppConst.DEEPLINK_INTERNAL_URL}${qrisPath}"

        fun getQrisLink(entryPoint: String? = null) =
            "${qrisLink}?${AnalyticsConst.ENTRY_POINT}=$entryPoint"
    }

    override val paths: Set<String> = setOf(qrisPath)

    override fun handle(signal: Signal) {
        val context = signal.context
        val navigator = signal.navigator

        val entryPoint =
            signal.query[AnalyticsConst.ENTRY_POINT] as? String ?: AnalyticsConst.DEEPLINK

        checkIsLogin(context) {
            when {
                PaymentUtils.isUpdateRequired(PaymentConst.Feature.QRIS) -> {
                    getFragmentManager(context)?.let {
                        UpdateBottomSheet.createInstance(PaymentConst.Feature.QRIS, entryPoint)
                            .show(it, UpdateBottomSheet.TAG)
                    }
                }
                PaymentUtils.shouldBeBlockedAsPerKycTier(PaymentConst.KYC_QRIS) -> {
                    getFragmentManager(context)?.let {
                        PaymentUtils.showKycKybStatusBottomSheet(it, entryPoint)
                    }
                }
                else -> {
                    getQrisData(context, signal.query, navigator)
                }
            }
        }
    }

    private fun getFragmentManager(context: Context): FragmentManager? {
        return try {
            (context as AppCompatActivity).supportFragmentManager
        } catch (ex: Exception) {
            FirebaseCrashlytics.getInstance().recordException(ex)
            null
        }
    }

    private fun getQrisData(context: Context, queries: Map<String, Any>, navigator: Navigator) {
        CoroutineScope(Dispatchers.IO).launch {
            when (val res = bankingUseCase.getQrisStatus(SessionManager.getInstance().businessId)) {
                is ApiSuccessResponse -> {
                    res.body.let {
                        val qrisInfo = QrisInfoSubset(
                            virtualAccountId = it.virtualAccountId, kycStatus = it.kycStatus,
                            kybStatus = it.kybStatus,
                            qrisStatus = it.qrisStatus, qrisCode = it.qrisCode,
                            matchingStatus = it.matchingStatus, finalStatus = it.finalStatus,
                            qrisBookId = it.metadata?.qris?.paymentBookId,
                            qrisBookName = it.metadata?.qris?.businessName
                        )
                        handleQrisRedirection(context, queries, navigator, qrisInfo)
                    }
                }
                is ApiErrorResponse -> {
                    FirebaseCrashlytics.getInstance().recordException(
                        Exception("ApiException in api: api/qris/status, message: ${res.errorMessage}")
                    )
                }
            }
        }
    }

    private fun handleQrisRedirection(
        context: Context, queries: Map<String, Any>, navigator: Navigator,
        qrisInfo: QrisInfoSubset
    ) {
        val entryPoint = (queries[AnalyticsConst.ENTRY_POINT] as? String) ?: AnalyticsConst.DEEPLINK
        val entrySource =
            (queries[AnalyticsConst.ENTRY_SOURCE] as? String) ?: AnalyticsConst.DEEPLINK

        // entry_point prop is source of click i.e. qris_banner, qris_icon, qris_status
        // location prop is screen name i.e. pembayaran, homepage etc
        val qrisWebUrl = PaymentUtils.getQrisWebUrl()
        val qrisUrl = "${qrisWebUrl}?entry_point=$entryPoint&location=$entrySource"

        /**
         * Check if QRIS is discontinued
         */
        if (RemoteConfigUtils.getPaymentConfigs().isQrisDiscontinued.isTrue) {
            // Check if data is already saved in shared preferences
            PaymentPrefManager.getInstance().getQrisGroupData()?.let {
                if (it.status == WhitelistGroupStatus.ENABLED) {
                    context.startActivity(WebviewActivity.createIntent(context, qrisUrl, ""))
                    return
                }
            }
        }

        // Case 2: haven't submitted the QRIS
        if (qrisInfo.finalStatus == null) {
            navigator.navigate(WebviewActivity.createIntent(context, qrisUrl, ""))
            return
        }

        val activeBook = SessionManager.getInstance().businessId
        when (qrisInfo.finalStatus) {
            // Case 5: QRIS is REJECTED
            QrisAndKycStatus.REJECTED.name -> {
                when {
                    qrisInfo.matchingStatus == NameMatchingStatus.FAILED_VERIFICATION.name
                            || qrisInfo.kybStatus == VerificationStatus.FAILED_VERIFICATION.name -> {
                        // Case 5d: QRIS is rejected due to name matching failure
                        redirectToQrIfVerified(
                            navigator, context, activeBook, qrisInfo, entryPoint, qrisUrl
                        )
                    }
                    qrisInfo.qrisStatus == VerificationStatus.FAILED_VERIFICATION.name ->
                        // 5a. Both KYC and QRIS is rejected
                        navigator.navigate(WebviewActivity.createIntent(context, qrisUrl, ""))
                    qrisInfo.kycStatus == VerificationStatus.FAILED_VERIFICATION.name -> {
                        // 5b. KYC is rejected, QRIS is pending/verified
                        val url = "${PaymentUtils.getQrisWebUrl()}?onlyKyc=true"
                        navigator.navigate(WebviewActivity.createIntent(context, url, ""))
                    }
                    else -> {
                        // 5e. Ideally shouldn't reach here but in case rejection is due to
                        // some unknown reason
                        navigator.navigate(WebviewActivity.createIntent(context, qrisUrl, ""))
                    }
                }
            }
            // Case 4: QRIS is VERIFIED
            QrisAndKycStatus.VERIFIED.name -> {
                redirectToQrIfVerified(
                    navigator, context, activeBook, qrisInfo, entryPoint, qrisUrl
                )
            }
            // Case 3: QRIS is PENDING
            else -> {
                when {
                    // Case 3a -> When QRIS & KYC status is verified
                    qrisInfo.isApprovedQrisUser() -> {
                        redirectToQrIfVerified(
                            navigator, context, activeBook, qrisInfo, entryPoint, qrisUrl
                        )
                    }
                    else -> {
                        // Case 3aa -> When pending due to QRIS or KYC status + new flow
                        navigator.navigate(WebviewActivity.createIntent(context, qrisUrl, ""))
                    }
                }
            }
        }
    }

    /**
     * We allow user to see QRIS only if they have KYC and QRIS approved and have a QR code
     */
    private fun redirectToQrIfVerified(
        navigator: Navigator, context: Context, activeBook: String,
        qrisInfo: QrisInfoSubset, entryPoint: String, qrisUrl: String
    ) {
        if (qrisInfo.isApprovedQrisUser()) {
            if (activeBook == qrisInfo.qrisBookId) {
                navigator.navigate(QrisActivity.createIntent(context, null, entryPoint))
            } else {
                showBookSwitchBottomSheet(context, qrisInfo, entryPoint)
            }
        } else {
            navigator.navigate(WebviewActivity.createIntent(context, qrisUrl, ""))
        }
    }

    /**
     * A utility method that shows SwitchToQrisBookBottomSheet if required info is available
     */
    private fun showBookSwitchBottomSheet(
        context: Context, qrisInfo: QrisInfoSubset, entryPoint: String
    ) {
        getFragmentManager(context)?.let {
            Utilities.safeLet(qrisInfo.qrisBookName, qrisInfo.qrisBookId) { bookName, bookId ->
                SwitchToQrisBookBottomSheet.createInstance(bookName, bookId, entryPoint)
                    .show(it, SwitchToQrisBookBottomSheet.TAG)
            }
        }
    }
}