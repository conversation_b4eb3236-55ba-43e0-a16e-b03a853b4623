package com.bukuwarung.payments.pin

import android.os.CountDownTimer
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.PaymentTwoFAUseCase
import com.bukuwarung.enums.AuthAction
import com.bukuwarung.model.request.PinForgetRequest
import com.bukuwarung.model.request.PinSetupRequest
import com.bukuwarung.model.request.PinUpdateRequest
import com.bukuwarung.payments.constants.PinType
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.Utilities
import com.bukuwarung.utils.isTrue
import com.bukuwarung.utils.orDefault
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class NewPaymentPinViewModel @Inject constructor(
    private var paymentTwoFAUseCase: PaymentTwoFAUseCase,
    private var sessionManager: SessionManager
) : ViewModel() {

    private var lastPinChangeDate = ""
    private var otpChannel = AppConst.SMS
    private var createPinViewSelected = false
    private var confirmPinViewSelected = false
    private var currentPin = ""
    private var createNewPin = ""
    private var confirmNewPin = ""
    private var pinLength: Int = 6
    private var useCase: PinType = PinType.PIN_CONFIRM
    private var timer: CountDownTimer? = null
    private val _viewSharedFlow = MutableSharedFlow<ViewState>(replay = 1)
    val viewSharedFlow = _viewSharedFlow.asSharedFlow()

    data class ViewState(
        val showLoader: Boolean = false,
        val showOtpView: Boolean = false,
        val showConfirmPinView: Boolean = false,
        val showPinUpdateView1: Boolean = false,
        val showPinUpdateView2: Boolean = false,
        val createPinViewSelected: Boolean = false,
        val confirmPinViewSelected: Boolean = false,
        val enablePinUpdateButton: Boolean = false,
        val otpError: String = "",
        val pinApiError: String = "",
        val otpTimeInSecs: Int? = null,
        val pinConfirmationError: String = "",
        val pinPatternError: Boolean = false,
        val pinMatchingOldPin: Boolean = false,
        val pinUpdateError: String = "",
        val pinLength: Int = 6,
        val currentPin: String = "",
        val createNewPin: String = "",
        val confirmNewPin: String = "",
        val pinConfirmationSuccessful: Boolean = false,
        val internetError: Boolean = false,
        val serverError: Boolean = false,
        val errorContent: String = "",
        val livelinessExpired: Boolean = false,
        val checkoutToken: String = "",
        val otpChannel: String = AppConst.SMS,
        val lastPinChangeDate: String = "",
        val changePinStatus: String = "",
        val checkChangePinStatus: Boolean = false
    )

    fun onEventReceived(paymentPinIntent: PaymentPinIntent) {
        when (paymentPinIntent) {
            is PaymentPinIntent.OnCreateView -> handleOnCreateView(paymentPinIntent.useCase, paymentPinIntent.skipOtpCall)
            is PaymentPinIntent.OnVerifyOTP -> verifyOtp(paymentPinIntent.otp)
            is PaymentPinIntent.OnRequestOTP -> requestOtp(paymentPinIntent.forceRequest)
            is PaymentPinIntent.AddDigit -> addDigitToPin(paymentPinIntent.digit)
            PaymentPinIntent.DeleteDigit -> deleteDigitFromPin()
            PaymentPinIntent.ConfirmNewPin -> {
                when(useCase){
                    PinType.PIN_CREATE, PinType.PIN_CONFIRM -> createPin()
                    PinType.PIN_UPDATE -> updatePin()
                    PinType.PIN_FORGOT, PinType.PIN_FORGOT_ENTER_OTP_STEP, PinType.PIN_FORGOT_CREATE_PIN_STEP -> forgotPin()
                }
            }
            PaymentPinIntent.ClickedCreatePinView -> pinViewSelected(isCreatePinView = true)
            PaymentPinIntent.ClickedConfirmPinView -> pinViewSelected(isCreatePinView = false)
            PaymentPinIntent.ChangeOtpChannel -> changeOtpChannel()
            else -> {}
        }
    }
    private fun pinViewSelected(isCreatePinView: Boolean) = viewModelScope.launch {
        createPinViewSelected = isCreatePinView
        confirmPinViewSelected = !isCreatePinView
        _viewSharedFlow.tryEmit(ViewState(showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected, createNewPin = createNewPin, confirmNewPin = confirmNewPin))
    }

    private fun addDigitToPin(digit: Int) = viewModelScope.launch {
        if (createPinViewSelected){
            if (createNewPin.length < 6) createNewPin = createNewPin.plus(digit)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected))
            if (createNewPin.length == 6){
                _viewSharedFlow.emit(ViewState(showLoader = true))
                if (createNewPin == currentPin) {
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, pinMatchingOldPin = true))
                } else if (Utilities.checkIfPinNotAllowed(createNewPin)){
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, pinPatternError = true))
                } else {
                    confirmPinViewSelected = true
                    createPinViewSelected = false
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
                }
            }
        } else if (confirmPinViewSelected) {
            if (confirmNewPin.length < 6) confirmNewPin = confirmNewPin.plus(digit)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
            if (confirmNewPin.length == 6){
                if (createNewPin == confirmNewPin) {
                    _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected, enablePinUpdateButton = true))
                } else {
                    showPinErrorView("Konfirmasi PIN tidak sesuai. Silakan cek ulang.")
                }

            }
        } else {
            if (currentPin.length < pinLength) currentPin = currentPin.plus(digit)
            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate))
            if (currentPin.length == pinLength){
                _viewSharedFlow.emit(ViewState(showLoader = true))
                when(val result = paymentTwoFAUseCase.verifyPin(currentPin)){
                    is ApiSuccessResponse -> {
                        if (useCase == PinType.PIN_UPDATE)
                            requestOtp()
                        else
                            _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true, checkoutToken = result.body.token.orEmpty()))
                    }
                    is ApiErrorResponse -> {
                        currentPin = ""
                        _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, lastPinChangeDate = lastPinChangeDate, pinConfirmationError = result.errorMessage))
                    }
                    else -> {}
                }
            }
        }
    }

    private fun deleteDigitFromPin() = viewModelScope.launch{
        if (createPinViewSelected){
            createNewPin = createNewPin.dropLast(1)
            confirmNewPin = ""
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
        } else if (confirmPinViewSelected){
            confirmNewPin = confirmNewPin.dropLast(1)
            _viewSharedFlow.emit(ViewState(createNewPin = createNewPin, confirmNewPin = confirmNewPin, showPinUpdateView1 = true, showPinUpdateView2 = true, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
        } else {
            currentPin = currentPin.dropLast(1)
            _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, lastPinChangeDate = lastPinChangeDate, currentPin = currentPin))
        }
    }

    private fun handleOnCreateView(useCase: PinType, skipOtpCall: Boolean) {
        <EMAIL> = useCase
        when(useCase){
            PinType.PIN_FORGOT_ENTER_OTP_STEP -> { requestOtp() }
            PinType.PIN_FORGOT_CREATE_PIN_STEP -> { showPinUpdateView1() }
            PinType.PIN_UPDATE -> { checkPinLength() }
            PinType.PIN_CREATE -> { requestOtp() }
            PinType.PIN_CONFIRM -> { checkPinLength() }
            PinType.PIN_FORGOT -> { requestOtp(skipOtpCall) }
            else -> {}
        }
    }

    private fun showPinUpdateView1() = viewModelScope.launch{
        createPinViewSelected = true
        _viewSharedFlow.emit(ViewState(showPinUpdateView1 = true, createPinViewSelected = createPinViewSelected))
    }

    private fun checkPinLength() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        when(val result = paymentTwoFAUseCase.checkPinLength()){
            is ApiSuccessResponse -> {
                if (result.body.success.isTrue){
                    pinLength = result.body.pinLength.orDefault(6)
                    lastPinChangeDate = result.body.lastPinChangeDate.orEmpty()
                    _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, lastPinChangeDate = lastPinChangeDate))
                } else {
                    requestOtp()
                }
            }
            is ApiErrorResponse -> {
                if (result.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                    _viewSharedFlow.emit(ViewState(serverError = true, errorContent = result.errorMessage))
                else
                    _viewSharedFlow.emit(ViewState(serverError = false, errorContent = result.errorMessage))
            }
            else -> {}
        }
    }

    private fun requestOtp(skipApiCall: Boolean = false) = viewModelScope.launch {
        if (skipApiCall){
            setUiForOtp()
        } else {
            _viewSharedFlow.emit(ViewState(showLoader = true))
            when(val result = paymentTwoFAUseCase.requestOtpForPIN(otpChannel, getAuthAction())){
                is ApiSuccessResponse -> {
                    sessionManager.opToken = result.body.token
                    setUiForOtp()
                }
                is ApiErrorResponse -> {
                    if (result.statusCode == 409 || result.statusCode == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                    else _viewSharedFlow.emit(ViewState(showOtpView = true,otpError = result.errorMessage, otpChannel = otpChannel))
                }
                else -> {}
            }
        }
    }

    private fun setUiForOtp() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showOtpView = true, otpTimeInSecs = PaymentConst.OTP_WAIT_TIME_IN_SECONDS, otpChannel = otpChannel))
        val waitTime = PaymentConst.OTP_WAIT_TIME_IN_SECONDS
        timer = object :
            CountDownTimer(waitTime * PaymentConst.ONE_SECOND, PaymentConst.ONE_SECOND) {
            override fun onTick(millisUntilFinished: Long) {
                viewModelScope.launch{
                    _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel, otpTimeInSecs = (millisUntilFinished / PaymentConst.ONE_SECOND).toInt()))
                }
            }

            override fun onFinish() {
                viewModelScope.launch{
                    _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel))
                }
            }
        }
        timer!!.start()
    }

    private fun verifyOtp(otp: String) = viewModelScope.launch{
        timer?.cancel()
        //show loader
        _viewSharedFlow.emit(ViewState(showLoader = true))
        when(val result = paymentTwoFAUseCase.verifyOtp(otp, getAuthAction())){
            is ApiSuccessResponse -> {
                sessionManager.opToken = result.body.token
                showPinUpdateView1()
            }
            is ApiErrorResponse -> {
                _viewSharedFlow.emit(ViewState(showOtpView = true, otpChannel = otpChannel, otpError = result.errorMessage))
            }
            else -> {}
        }
    }

    private fun getAuthAction(): String{
        return when(useCase){
            PinType.PIN_CREATE, PinType.PIN_CONFIRM -> AuthAction.CREATE_SALDO_PIN_V3.value
            PinType.PIN_UPDATE -> AuthAction.UPDATE_SALDO_PIN_V3.value
            PinType.PIN_FORGOT, PinType.PIN_FORGOT_CREATE_PIN_STEP, PinType.PIN_FORGOT_ENTER_OTP_STEP -> AuthAction.FORGOT_SALDO_PIN_V3.value
            else -> {}
        }
    }

    private fun showPinErrorView(message: String) = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showPinUpdateView1 = true, showPinUpdateView2 = true, pinUpdateError = message, createNewPin = createNewPin, confirmNewPin = confirmNewPin, createPinViewSelected = createPinViewSelected, confirmPinViewSelected = confirmPinViewSelected))
    }

    private fun createPin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinCreateRequest = PinSetupRequest(pin = confirmNewPin)
        when(val result = paymentTwoFAUseCase.createPinV3(pinCreateRequest)){
            is ApiSuccessResponse -> {
                _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true, checkoutToken = result.body.token.orEmpty()))
            }
            is ApiErrorResponse -> {
                if (result.statusCode == 409 || result.statusCode == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                else showPinErrorView(result.errorMessage)
            }
            else -> {}
        }
    }

    private fun updatePin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinUpdateRequest = PinUpdateRequest(oldPin = currentPin, newPin = confirmNewPin)
        when(val result = paymentTwoFAUseCase.updatePinV3(pinUpdateRequest)){
            is ApiSuccessResponse -> {
                _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true))
            }
            is ApiErrorResponse -> {
                if (result.statusCode == 409 || result.statusCode == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                else showPinErrorView(result.errorMessage)
            }
            else -> {}
        }
    }

    private fun forgotPin() = viewModelScope.launch{
        _viewSharedFlow.emit(ViewState(showLoader = true))
        val pinForgotPin = PinForgetRequest(pin = confirmNewPin)
        when(val result = paymentTwoFAUseCase.forgotPinV3(pinForgotPin)){
            is ApiSuccessResponse -> {
                _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true))
            }
            is ApiErrorResponse -> {
                if (result.statusCode == 409  || result.statusCode == 402) _viewSharedFlow.emit(ViewState(livelinessExpired = true))
                else showPinErrorView(result.errorMessage)
            }
            is ApiEmptyResponse -> {
                _viewSharedFlow.emit(ViewState(pinConfirmationSuccessful = true))
            }
            else -> {}
        }
    }

    private fun changeOtpChannel(){
        otpChannel = if (otpChannel == AppConst.SMS) AppConst.WA else AppConst.SMS
        requestOtp()
    }

    fun checkPinChangeRequest() = viewModelScope.launch(Dispatchers.IO) {
        when(val result = paymentTwoFAUseCase.checkPinChangeRequest()){
            is ApiSuccessResponse -> {
                _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate, checkChangePinStatus = true, changePinStatus = result.body.content?.firstOrNull()?.status.orEmpty()))
            }
            is ApiErrorResponse -> {
                _viewSharedFlow.emit(ViewState(showConfirmPinView = true, pinLength = pinLength, currentPin = currentPin, lastPinChangeDate = lastPinChangeDate, checkChangePinStatus = true, changePinStatus = ""))
            }
            else -> {}
        }
    }
}