package com.bukuwarung.payments.viewmodels

import android.os.Parcelable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.UserProfileEntity
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.FinproOrderResponse
import com.bukuwarung.payments.data.model.PaymentHistory
import com.bukuwarung.payments.data.model.payInDisbursableType
import com.bukuwarung.payments.data.model.qrisDisbursableType
import com.bukuwarung.session.SessionManager
import kotlinx.android.parcel.Parcelize
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class AssistPageViewModel @Inject constructor(
    private val businessUseCase: BusinessUseCase, private val finproUseCase: FinproUseCase,
) : BaseViewModel() {

    private var paymentType: String? = null
    private var disbursableType: String? = null

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(
        ViewState()
    )

    private val productDetail = MutableLiveData<DetailEvent>()
    val observeDetail: LiveData<DetailEvent> = productDetail

    @Parcelize
    data class ViewState(
        val loader: Boolean = true,
        val serverError: Boolean = false,
        val internetError: Boolean = false,
        val retryLoader: Boolean = false,
    ) : Parcelable

    sealed class DetailEvent {
        data class ApiError(val isServerError: Boolean, val errorMessage: String) :
            DetailEvent()

        data class ShowProductData(val detail: FinproOrderResponse) :
            DetailEvent()
    }


    fun init(paymentType: String?, disbursableType: String?) {
        this.paymentType = paymentType
        this.disbursableType = disbursableType
    }

    fun isPaymentIn(): Boolean {
        return if (disbursableType.isNullOrBlank()) {
            paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
        } else {
            return paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
                    && disbursableType == payInDisbursableType
        }
    }

    fun isQrisPaymentIn(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
                && disbursableType == qrisDisbursableType
    }

    fun isPaymentOut(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_PAYMENT_OUT, ignoreCase = true)
    }

    fun isPaymentSaldoIn(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_IN, ignoreCase = true)
    }

    fun isSaldoRefund() = paymentType.equals(PaymentHistory.TYPE_SALDO_REFUND, ignoreCase = true)

    fun isPaymentSaldoOut(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_OUT, ignoreCase = true)
    }

    fun isPaymentPpob(): Boolean {
        return !isPaymentIn() && !isQrisPaymentIn() && !isPaymentOut() && !isPaymentSaldoIn() && !isPaymentSaldoOut() && !isSaldoRefund()
    }

    fun getUserProfile(userId: String): UserProfileEntity? {
        return businessUseCase.getUserProfileEntity(userId)
    }

    fun getOrderDetail(orderId: String, ledgerAccountId: String?) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response =
                finproUseCase.getOrderDetail(SessionManager.getInstance().businessId, orderId, ledgerAccountId)) {
                is ApiSuccessResponse -> {
                   withContext(Dispatchers.Main) { productDetail.value = DetailEvent.ShowProductData(response.body) }
                }
                is ApiErrorResponse -> handleApiError(response)
            }
        }
    }

    private suspend fun <T> handleApiError(response: ApiErrorResponse<T>) {
        withContext(Dispatchers.Main) {
            if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE)
                viewState.value = currentViewState().copy(
                    loader = false,
                    serverError = true,
                    internetError = false,
                    retryLoader = false
                )
            else viewState.value = currentViewState().copy(
                loader = false,
                serverError = false,
                internetError = true,
                retryLoader = false
            )
        }
    }
}