package com.bukuwarung.payments.viewmodels

import android.content.Context
import android.os.CountDownTimer
import android.os.Parcelable
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.Application
import com.bukuwarung.R
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.activities.phonebook.model.Contact
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.base_android.extensions.postEvent
import com.bukuwarung.base_android.utils.Event
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.constants.AppConst.EMPTY_STRING
import com.bukuwarung.constants.PaymentConst
import com.bukuwarung.constants.PaymentConst.ALFAMART
import com.bukuwarung.constants.PaymentConst.BCA
import com.bukuwarung.constants.PaymentConst.BNI
import com.bukuwarung.constants.PaymentConst.BRI
import com.bukuwarung.constants.PaymentConst.CIMB
import com.bukuwarung.constants.PaymentConst.DANA
import com.bukuwarung.constants.PaymentConst.GOPAY
import com.bukuwarung.constants.PaymentConst.LINKAJA
import com.bukuwarung.constants.PaymentConst.MANDIRI
import com.bukuwarung.constants.PaymentConst.OVO
import com.bukuwarung.constants.PaymentConst.PERMATA
import com.bukuwarung.constants.PaymentConst.QRIS
import com.bukuwarung.constants.PaymentConst.SALDO
import com.bukuwarung.constants.PaymentConst.SALDO_CAMEL
import com.bukuwarung.constants.PaymentConst.SHOPEEPAY
import com.bukuwarung.data.restclient.ApiEmptyResponse
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.database.entity.*
import com.bukuwarung.database.repository.BusinessRepository
import com.bukuwarung.database.repository.CashRepository
import com.bukuwarung.domain.business.BusinessUseCase
import com.bukuwarung.domain.customer.CustomerUseCase
import com.bukuwarung.domain.payments.*
import com.bukuwarung.domain.transaction.TransactionUseCase
import com.bukuwarung.payments.constants.PpobConst
import com.bukuwarung.payments.constants.PpobConst.ADD_TO_FAV_NUDGE
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_INTERNET_DAN_TV_CABLE
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_MULTIFINANCE
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_PDAM
import com.bukuwarung.payments.constants.PpobConst.CATEGORY_VEHICLE_TAX
import com.bukuwarung.payments.constants.PpobConst.DISABLE
import com.bukuwarung.payments.data.model.*
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_COMPLETED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_FAILED
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_PAID
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_PENDING
import com.bukuwarung.payments.data.model.PaymentHistory.Companion.STATUS_REFUNDED
import com.bukuwarung.payments.pref.PaymentPrefManager
import com.bukuwarung.preference.FeaturePrefManager
import com.bukuwarung.session.SessionManager
import com.bukuwarung.session.User
import com.bukuwarung.setup.CashTransactionDataSync
import com.bukuwarung.setup.PaymentCashTransactionDataSync
import com.bukuwarung.setup.SetupManager
import com.bukuwarung.utils.*
import kotlinx.android.parcel.Parcelize
import kotlinx.android.parcel.RawValue
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.util.Date
import javax.inject.Inject

class PaymentHistoryDetailViewModel @Inject constructor(
    private val paymentUseCase: PaymentUseCase,
    private val customerUseCase: CustomerUseCase,
    private val finproUseCase: FinproUseCase,
    private val cashRepository: CashRepository,
    private val businessUseCase: BusinessUseCase,
    private val transactionUseCase: TransactionUseCase
) : BaseViewModel() {

    @Parcelize
    data class ViewState(
        val loader: Boolean = true,
        val amount: Double = 0.0,
        val agentFeeInfo: @RawValue AgentFeeInfo? = null,
        val buyingPrice: Double = 0.0,//for event purpose
        val status: String? = null,
        val metaData: PpobMetaData? = null,
        val hasPaidStatus: Boolean = false,
        val completedStatusDate: String = "",
        val isPaymentCompleted: Boolean = false,
        val receiverBank: ReceiverBank? = null,
        val paymentChannel: String? = "",
        val transactionId: String = "",
        val transactionNote: String = "-",
        val isFailed: Boolean = false,
        val discount: Float = 0f,
        val userId: String? = User.getUserId(),
        val disburseId: String? = "",
        val paymentCategory: PaymentCategoryItem? = null,
        val paymentCollectionInfo: PaymentCollectionInfo? = null,
        //extra variables used for pulsa feature
        val orderId: String? = "",
        val ppobCategory: String = "",
        val ppobItem: PpobProductDetail? = null,
        val loyaltyDiscount: Double = 0.0,
        val subscriptionDiscount: Double = 0.0,
        val bookEntity: @RawValue BookEntity? = BusinessRepository.getInstance(Application.getAppContext())
            .getBusinessByIdSync(User.getBusinessId()),
        val serverError: Boolean = false,
        val internetError: Boolean = false,
        val retryLoader: Boolean = false,
        val saldoBalance:Double? = null,
        val transactionDetails: FinproOrderResponse? = null,
        val disbursementTimings: DisbursementTimings? = null
    ) : Parcelable

    sealed class DetailEvent {
        data class ApiError(val isServerError: Boolean, val errorMessage: String) : DetailEvent()
        data class ShowProductData(val detail: FinproOrderResponse) : DetailEvent()
        data class UpdateTheTimerMessage(val pendingTime: String) : DetailEvent()
        object ShowPendingTrxHelpOption : DetailEvent()
        object ShowPendingTrxHelpOptionInRed : DetailEvent()
        data class OnHelpClicked(
            val isPpob: Boolean,
            val paymentAmount: Double,
            val userId: String?,
            val paymentId: String,
            val isFailed: Boolean,
            val hasPaidStatus: Boolean
        ) : DetailEvent()

        data class OnInstructionClicked(
            val url: String?,
            val prop: AppAnalytics.PropBuilder?,
            val paymentMethod: String? = null,
            val customerPhoneNumber: String? = null
        ) : DetailEvent()

        data class ShowCashTransactionLayout(val cashTransactionEntity: CashTransactionEntity?, val isPpob: Boolean) :
            DetailEvent()

        data class UpdateServiceFee(val amountFeeInfo: AgentFeeInfo) : DetailEvent()
        data class ShowToast(val message: String? = null, val stringId: Int = 0) : DetailEvent()
        object refreshScreen: DetailEvent()
        object OnQrisBankSet: DetailEvent()
        object ShowAddFavDialog: DetailEvent()
        data class RefundBankSet(val refundBank: RefundBankAccount): DetailEvent()
        data class FinishActivity(val missingBusinessId: Boolean, val missingOrderId: Boolean): DetailEvent()
    }

    private var contact: Contact? = null
    private var customerId: String? = null
    private var orderId: String? = null
    var paymentType: String? = null
    private var ledgerAccountId: String? = null
    private var disbursableType: String? = null
    private var trxStatus: String? = ""

    private val _navigation = MutableLiveData<Event<NavigationCommand>>()
    val navigation: LiveData<Event<NavigationCommand>> = _navigation

    private var _selectedBankAccount = MutableLiveData<List<BankAccount>>()
    val selectedBankAccount: LiveData<List<BankAccount>> = _selectedBankAccount

    private val productDetail = MutableLiveData<DetailEvent>()
    var orderResponse: FinproOrderResponse? = null
    val observeDetail: LiveData<DetailEvent> = productDetail
    private var ppobCategory: String = ""

    private var message: String? = null
    private var shareStatusMessage: String? = null
    private var timer: CountDownTimer? = null
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())

    var customer: CustomerEntity? = null


    private fun currentViewState(): ViewState = viewState.value!!

    fun init(
        customerId: String?,
        orderId: String?,
        paymentType: String?,
        refresh: Boolean = false,
        shareTemplate: Boolean = false,
        ledgerAccountId: String? = null
    ) {
        viewState.value = currentViewState().copy(loader = true)
        if(this.customerId.isNullOrBlank())
            this.customerId = if(customerId.isNotNullOrBlank()) customerId else transactionUseCase.getCustomerId(orderId?:"")
        customer = customerUseCase.getCustomerById(this.customerId)
        this.orderId = orderId
        this.paymentType = paymentType
        this.ledgerAccountId = ledgerAccountId
        getOrderDetail(refresh, shareTemplate)
    }

    fun isPaymentIn(): Boolean {
        return if (disbursableType.isNullOrBlank()) {
            paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
        } else {
            return paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
                    && disbursableType == payInDisbursableType
        }
    }

    fun isTrainTicket() =
        orderResponse?.items?.firstOrNull()?.beneficiary?.category == PpobConst.CATEGORY_TRAIN_TICKET

    fun isQrisPaymentIn(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_PAYMENT_IN, ignoreCase = true)
                && disbursableType == qrisDisbursableType
    }

    fun isPaymentOut(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_PAYMENT_OUT, ignoreCase = true)
    }

    fun isPaymentOutSubscription(): Boolean{
        return paymentType.equals(PaymentConst.TYPE_PAYMENT_OUT_SUBSCRIPTION, ignoreCase = true)
    }

    fun isPaymentSaldoIn(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_IN, ignoreCase = true)
    }

    fun isSaldoRefund() = paymentType.equals(PaymentHistory.TYPE_SALDO_REFUND, ignoreCase = true)

    fun isPaymentSaldoOut(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_OUT, ignoreCase = true)
    }

    fun isPaymentOutSaldo(): Boolean {
        return orderResponse?.payments?.firstOrNull()?.paymentMethod?.code.equals(
            SALDO,
            ignoreCase = true
        )
    }

    fun isPaymentPpob(): Boolean {
        return !isPaymentIn() && !isQrisPaymentIn() && !isPaymentOut() && !isPaymentSaldoIn() && !isPaymentSaldoOut() && !isPaymentSaldoRedemption() && !isPaymentSaldoCashback() && !isSaldoRefund() && !isPaymentOutSubscription()
    }

    fun isWebviewPpob(): Boolean {
        return paymentType !in listOf(
            PpobConst.CATEGORY_TRAIN_TICKET,
            PpobConst.CATEGORY_VOUCHER_GAME
        )
    }

    fun isPaymentSaldoRedemption(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_REDEMPTION, ignoreCase = true)
    }

    fun isPaymentSaldoCashback(): Boolean {
        return paymentType.equals(PaymentHistory.TYPE_SALDO_CASHBACK, ignoreCase = true)
                || paymentType.equals(PaymentConst.TYPE_CASHBACK_IN, ignoreCase = true)
                || paymentType.equals(PaymentConst.TYPE_CASHBACK_OUT, ignoreCase = true)
    }

    fun isSaldoBonusOut(): Boolean {
        return paymentType.equals(PaymentConst.TYPE_CASHBACK_OUT, ignoreCase = true)
    }

    override fun onCleared() {
        super.onCleared()
        timer?.cancel()
    }

    fun onHelpClicked() {
        productDetail.value = DetailEvent.OnHelpClicked(
            isPaymentPpob(),
            currentViewState().amount,
            currentViewState().userId,
            currentViewState().transactionId,
            currentViewState().isFailed,
            currentViewState().hasPaidStatus
        )
    }

    fun getCustomerName() = customer?.name ?: orderResponse?.customer?.customerName.orEmpty()

    fun onInstructionClicked() {
        orderResponse?.run {
            val paymentMethod = this.payments?.firstOrNull()?.paymentMethod?.code
            val category = this.items?.firstOrNull()?.beneficiary?.category
            val prop = AppAnalytics.PropBuilder()
            prop.put(AnalyticsConst.ENTRY_POINT2, AnalyticsConst.RETRY)
            prop.put(AnalyticsConst.ORDER_ID, this.orderId)
            prop.put(
                AnalyticsConst.PROVIDER,
                if ( category == CATEGORY_PDAM || category == CATEGORY_MULTIFINANCE || category == CATEGORY_INTERNET_DAN_TV_CABLE || category == CATEGORY_VEHICLE_TAX) {
                    this.items?.firstOrNull()?.name
                } else {
                    this.items?.firstOrNull()?.beneficiary?.code
                }
            )
            prop.put(AnalyticsConst.PACKAGE_PRICE, this.amount)
            prop.put(AnalyticsConst.BUYING_PRICE_SNAKE_CASE, (this.amount?: AppConst.ZERO_DOUBLE) + (this.fee?: AppConst.ZERO_DOUBLE))
            prop.put(AnalyticsConst.SELLING_PRICE, this.items?.firstOrNull()?.sellingPrice)
            prop.put(AnalyticsConst.PAYMENT_METHOD, paymentMethod.orEmpty().lowercase())
            prop.put(AnalyticsConst.PAYMENT_STATUS, AnalyticsConst.INCOMPLETE)
            prop.put(AnalyticsConst.PPOB_STATUS, AnalyticsConst.INCOMPLETE)
            val checkoutUrl = this.payments?.firstOrNull()?.checkoutUrl ?: this.payments?.firstOrNull()?.paymentUrl
            productDetail.value = DetailEvent.OnInstructionClicked(
                checkoutUrl,
                prop,
                paymentMethod,
                items?.firstOrNull()?.beneficiary?.number
            )
        }
    }

    private fun getOrderDetail(refresh: Boolean, shareTemplate: Boolean = false) = viewModelScope.launch {
        timer?.cancel()
        /**
         * Handling edge case when orderId can be null and user still reaches here
         */
        if (SessionManager.getInstance().businessId == null || orderId == null) {
            productDetail.value = DetailEvent.FinishActivity(
                SessionManager.getInstance().businessId == null,
                orderId == null
            )
            return@launch
        }
        when (val response = finproUseCase.getOrderDetail(SessionManager.getInstance().businessId, orderId!!, ledgerAccountId)) {
            is ApiSuccessResponse -> {
                orderResponse = response.body
                if (!shareTemplate) message =
                    if (orderResponse?.paymentCollectionInfo?.paymentCollection?.template.isNotNullOrBlank())
                        orderResponse?.paymentCollectionInfo?.paymentCollection?.template?.getPaymentSharingText(
                            customer
                        )
                    else
                        orderResponse?.template?.getPaymentSharingText(customer)
                orderResponse?.run {
                    // Note: Payments service also keeps customerId,
                    // here we replace customerId variable with the actual customerId from payments
                    if (this.customer?.customerId != null) {
                        customerId = this.customer.customerId
                    }
                    val sellingPrice = this.items?.firstOrNull()?.sellingPrice ?: 0.0
                    val amt = if (!isPaymentIn() && !isPaymentOut() && sellingPrice > 0) sellingPrice else amount
                    ppobCategory = items?.firstOrNull()?.beneficiary?.category ?: ""
                    val completedStatusDate = this.getCompletedStatusDate().orDash

                    val hasPaidStatus = this.progress?.firstOrNull { it.state == STATUS_PAID }?.timestamp != null
                    val hasRefundedStatus = this.progress?.lastOrNull()?.state == STATUS_REFUNDED
                    shareStatusMessage = response?.body?.shareUrlTemplate
                    val item = this.items?.firstOrNull()
                    disbursableType = item?.disbursableType
                    productDetail.value = DetailEvent.ShowProductData(this)
                    val qrisNote = getQrisNotes(this.description)
                    // Since customer will be null for saldo in and saldo out,
                    // we set the phone no of the customer entity to make it appear in the receipt
                    if (isPaymentSaldoIn() || isPaymentSaldoOut()) {
                        <EMAIL> =
                            CustomerEntity().apply { phone = item?.beneficiary?.phoneNumber }
                    }
                    viewState.value = currentViewState().copy(
                        agentFeeInfo = this.agentFeeInfo,
                        serverError = false,
                        internetError = false,
                        amount = amt?: AppConst.ZERO_DOUBLE,
                        buyingPrice = amount?: AppConst.ZERO_DOUBLE,
                        loader = false,
                        status = this.status,
                        metaData = this.metadata,
                        isPaymentCompleted = this.status == STATUS_COMPLETED && !hasRefundedStatus,
                        isFailed = this.status == STATUS_FAILED,
                        hasPaidStatus = hasPaidStatus,
                        disburseId = this.payments?.firstOrNull()?.paymentId,
                        transactionId = this.transactionId ?: "-",
                        transactionNote = if (isQrisPaymentIn()) qrisNote else this.description?:EMPTY_STRING,
                        orderId = this.orderId,
                        completedStatusDate = completedStatusDate ?: "-",
                        ppobCategory = ppobCategory,
                        paymentCategory = this.paymentCategory,
                        paymentCollectionInfo = this.paymentCollectionInfo,
                        ppobItem = items?.firstOrNull(),
                        loyaltyDiscount = loyalty?.tierDiscount.orNil,
                        subscriptionDiscount = loyalty?.subscriptionDiscount.orNil,
                        paymentChannel = this.payments?.getOrNull(0)?.paymentMethod?.code,
                        receiverBank = ReceiverBank(
                            bankCode = item?.beneficiary?.code,
                            accountNumber = item?.beneficiary?.accountNumber,
                            accountHolderName = item?.beneficiary?.name
                        )
                    )
                    if (!this.isCompleted() && this.channelPendingEta.isNotNullOrBlank()) {
                        // NOTE: For Saldo we show a static processing time.
                        if (items?.firstOrNull { it.name.isNotNullOrEmpty() }?.name == SALDO_CAMEL) {
                            val timeDiff =
                                DateTimeUtils.getUtcDateDiffInMillis(this.progress?.firstOrNull { it.state == STATUS_PENDING }?.timestamp ?: "", this.channelPendingEta ?: "")
                            productDetail.value = DetailEvent.UpdateTheTimerMessage(
                                DateTimeUtils.millisecondsToMinutes(timeDiff)
                            )
                        } else if (!isQrisPaymentIn() && this.status == STATUS_PAID)
                            initRetryTimer(
                                this.progress?.firstOrNull { it.state == STATUS_PAID }?.timestamp ?: ""
                            )
                    }
                    trackRefreshIconAnalytics(refresh, this.status ?: "", ppobCategory)
                    trxStatus = this.status ?: ""
                    if (shareTemplate) shareRequestPaymentTemplate()
                    if(flags?.get(ADD_TO_FAV_NUDGE).isTrue)
                        productDetail.value = DetailEvent.ShowAddFavDialog
                    // Fetch disbursement timings for pending qris transaction
                    if (isQrisPaymentIn() && (status == STATUS_PENDING || status == STATUS_PAID)) {
                        Utilities.safeLet(
                            items?.firstOrNull()?.beneficiary?.code, amount
                        ) { code, amount ->
                            getDisbursementTimings(code, createdAt, amount)
                        }
                    }
                }
            }
            is ApiErrorResponse -> handleApiError(response, false)
        }
    }

    private fun getDisbursementTimings(bankCode: String, createdAt: String?, amount: Double) =
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                when (val response =
                    paymentUseCase.getDisbursementTimings(bankCode, createdAt, amount)) {
                    is ApiSuccessResponse -> {
                        withContext(Dispatchers.Main) {
                            viewState.value =
                                currentViewState().copy(disbursementTimings = response.body)
                        }
                    }
                }
            }
        }

    private fun initRetryTimer(startDate: String) {
        //adding pending trx time from remote config to trx created time and comparing it to current time to show corresponding ui
        val startTimeInMillis = DateTimeUtils.getTimestampFromUtcDate(startDate)
        val pendingTrxTimeInMillis = DateTimeUtils.convertMinsToMillis(RemoteConfigUtils.getPaymentPendingTimeInMinutes())
        val currentTimeInMillis = DateTimeUtils.getCurrentUTCTime()
        val diffWithCurrentTime = startTimeInMillis + pendingTrxTimeInMillis - currentTimeInMillis
        if (diffWithCurrentTime > 0) {
            productDetail.value = DetailEvent.ShowPendingTrxHelpOption
            timer = object : CountDownTimer(diffWithCurrentTime, AppConst.ONE_SECOND) {
                override fun onTick(millisUntilFinished: Long) {
                }

                override fun onFinish() {
                    getOrderDetail(false, false)
                    timer?.cancel()
                }
            }
            timer!!.start()
        } else {
            productDetail.value = DetailEvent.ShowPendingTrxHelpOptionInRed
        }
    }

    fun shareRequestPaymentTemplate(shareStatus: Boolean = false) {
        _navigation.postEvent(
            NavigationCommand.ShareInvoiceCommand(
                if (shareStatus) shareStatusMessage
                    ?: "" else message ?: ""
            )
        )
    }

    fun updateAgentFee(agentFee: Double, description: String) {
        when {
            isPaymentIn() -> updatePaymentInAgentFee(agentFee, description)
            isPaymentOut() -> updatePaymentOutAgentFee(agentFee, description)
            isQrisPaymentIn() -> updateQrisInAgentFee(agentFee, description)
        }
    }

    private fun updatePaymentInAgentFee(amount: Double, description: String) = viewModelScope.launch {
        Utilities.safeLet(
            SessionManager.getInstance().businessId, customerId, orderId
        ) { accountId, customerId, orderId ->
            when (val response = paymentUseCase.updatePaymentInAgentFee(
                accountId, customerId, orderId, UpdateFeeRequest(amount, description)
            )) {
                is ApiSuccessResponse -> {
                    val collection = response.body
                    collection.agentFeeInfo?.let {
                        productDetail.value = DetailEvent.UpdateServiceFee(it)
                    }
                    message = response.body.template?.getPaymentSharingText(customer)
                    val completedStatusDate = if (collection.isCompleted()) {
                        collection.progress.firstOrNull { it.state == STATUS_COMPLETED }
                            ?.getFormattedTimestamp() ?: "-"
                    } else {
                        "-"
                    }
                    val amt = collection.amount.toDouble()
                    val updatedAt =
                        if (collection.agentFeeInfo?.updatedAt != null) DateTimeUtils.getFormattedLocalDateTimeForPayment(
                            collection.agentFeeInfo.updatedAt
                        ) else ""
                    viewState.value = currentViewState().copy(
                        amount = amt,
                        loader = false,
                        orderId = collection.paymentRequestId,
                        isPaymentCompleted = collection.isCompleted(),
                        isFailed = collection.status == PaymentHistory.STATUS_FAILED,
                        agentFeeInfo = AgentFeeInfo(
                            collection.agentFeeInfo?.amount
                                ?: 0.0, updatedAt
                        ),
                        receiverBank = collection.receiverBank,
                        transactionId = collection.transactionId
                            ?: "-",
                        transactionNote = collection.description
                            ?: "-", /*bookEntity = book,*/
                        discount = collection.discount,
                        hasPaidStatus = collection.isPaid(),
                        completedStatusDate = completedStatusDate,
                        paymentChannel = collection.paymentChannel,
                        paymentCategory = collection.paymentCategory,
                        disburseId = collection.paymentRequestId,
                        serverError = false,
                        internetError = false
                    )
                    //AsyncCashTransactionDataSync(false, orderId).execute()
                    CashTransactionDataSync(false, orderId).execute()
                }
                is ApiErrorResponse -> handleApiError(response)
                else -> {}
            }
        }
    }

    private fun updatePaymentOutAgentFee(amount: Double, description: String) = viewModelScope.launch {
        Utilities.safeLet(
            SessionManager.getInstance().businessId, customerId, orderId
        ) { accountId, customerId, orderId ->
            when (val response = paymentUseCase.updatePaymentOutAgentFee(
                accountId, customerId, orderId, UpdateFeeRequest(amount, description)
            )) {
                is ApiSuccessResponse -> {
                    val disbursement = response.body
                    disbursement.agentFeeInfo?.let {
                        productDetail.value = DetailEvent.UpdateServiceFee(it)
                    }
                    val completedStatusDate = if (disbursement.isCompleted()) {
                        disbursement.progress.firstOrNull { it.state == STATUS_COMPLETED }
                            ?.getFormattedTimestamp().orEmpty()
                    } else {
                        "-"
                    }
                    val amt = disbursement.amount.toDouble()

                    val updatedAt =
                        if (disbursement.agentFeeInfo?.updatedAt != null) DateTimeUtils.getFormattedLocalDateTimeForPayment(
                            disbursement.agentFeeInfo.updatedAt
                        ) else ""
                    viewState.value = currentViewState().copy(
                        amount = amt,
                        loader = false,
                        orderId = disbursement.disbursementId,
                        isPaymentCompleted = disbursement.isCompleted(),
                        isFailed = disbursement.status == PaymentHistory.STATUS_FAILED,
                        hasPaidStatus = disbursement.isPaid(),
                        agentFeeInfo = AgentFeeInfo(
                            disbursement.agentFeeInfo?.amount
                                ?: 0.0, updatedAt
                        ),
                        completedStatusDate = completedStatusDate,
                        receiverBank = disbursement.receiverBank,
                        transactionId = disbursement.transactionId
                            ?: "-",
                        transactionNote = disbursement.description
                            ?: "-", /*bookEntity = book,*/
                        discount = disbursement.discount,
                        paymentChannel = disbursement.paymentChannel,
                        paymentCategory = disbursement.paymentCategory,
                        disburseId = disbursement.disbursementId,
                        serverError = false,
                        internetError = false
                    )
                    //AsyncCashTransactionDataSync(false, orderId).execute()
                    CashTransactionDataSync(false, orderId).execute()
                }
                is ApiErrorResponse -> handleApiError(response)
                else -> {}
            }
        }
    }

    private fun updateQrisInAgentFee(amount: Double, description: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.updateQrisInAgentFee(
                SessionManager.getInstance().businessId,
                orderId!!,
                UpdateFeeRequest(amount, description)
            )) {
                is ApiSuccessResponse, is ApiEmptyResponse -> {
                    withContext(Dispatchers.Main) {
                        val agentFee = AgentFeeInfo(amount, DateTimeUtils.getTimeInUTC(Date()))
                        viewState.value = currentViewState().copy(
                            loader = false, serverError = false, internetError = false,
                            agentFeeInfo = agentFee,
                            transactionNote = getQrisNotes(description)
                        )
                        productDetail.value = DetailEvent.UpdateServiceFee(agentFee)
                    }
                }
                is ApiErrorResponse -> handleApiError(response)
            }
        }
    }

    private fun getQrisNotes(description: String?): String {
        return if (description.isNullOrEmpty() || description == "-")
            orderResponse?.customer?.customerName?.let { "via $it" } ?: run { "-" }
        else
            description.orEmpty()
    }

    fun changeRefundBank(refundBankAccount: RefundBankAccount) = viewModelScope.launch {
        val finproRefunds =
            FinproRefunds(paymentChannel = PaymentConst.BANK_TRANSFER, refundPaymentMethod = listOf(refundBankAccount))
        val accountId = SessionManager.getInstance().businessId
        when (val res = finproUseCase.changeRefundBankAccount(accountId, finproRefunds)) {
            is ApiSuccessResponse -> {
                setEventState(DetailEvent.RefundBankSet(refundBankAccount))
            }
            is ApiErrorResponse -> handleApiError(res)
        }
    }

    fun getTransactionDataByOrderId(orderId: String?, isPpob: Boolean) {
        viewModelScope.launch {
            productDetail.postValue(
                DetailEvent.ShowCashTransactionLayout(
                    cashRepository.getCategoryByOrderId(orderId),
                    isPpob
                )
            )
        }
    }

    sealed class NavigationCommand {
        class ShareInvoiceCommand(val paymentMessage: String) : NavigationCommand()
    }

    private fun trackRefreshIconAnalytics(refresh: Boolean, status: String?, paymentType: String) {
        if (refresh)
            AppAnalytics.trackEvent(
                AnalyticsConst.EVENT_CLICK_PAYMENT_REFRESH_STATUS,
                AppAnalytics.PropBuilder()
                    .put(AnalyticsConst.OLD_STATUS, trxStatus)
                    .put(AnalyticsConst.NEW_STATUS, status)
                    .put(AnalyticsConst.TYPE, paymentType), false, false, false
            )
    }

    fun getStatus(): String? {
        return trxStatus
    }

    fun getEWalletPaymentMethodInstruction(code: String): Int {
        val paymentInstruction = mutableMapOf(
            DANA to R.string.dana_payment_instruction,
            LINKAJA to R.string.linkaja_payment_instruction,
            OVO to R.string.ovo_payment_instruction,
            SHOPEEPAY to R.string.shopee_payment_instruction,
            QRIS to R.string.qris_payment_instruction,
            ALFAMART to R.string.alphamart_payment_instruction,
            GOPAY to R.string.gopay_payment_instruction
        )
        return paymentInstruction[code] ?: R.string.gopay_payment_instruction
    }


    fun getATMPaymentMethodInstruction(code: String): Int {
        val paymentInstruction = mutableMapOf(
            BCA to R.string.bca_atm_payment_instruction,
            BNI to R.string.bni_atm_payment_instruction,
            BRI to R.string.bri_atm_payment_instruction,
            MANDIRI to R.string.mandiri_atm_payment_instruction,
            PERMATA to R.string.permata_atm_payment_instruction,
            CIMB to R.string.cimb_atm_payment_instruction
        )
        return paymentInstruction[code] ?: R.string.bank_atm_payment_instruction
    }

    fun getIBankingPaymentMethodInstruction(code: String): Int {
        val paymentInstruction = mutableMapOf(
            BCA to R.string.bca_i_banking_payment_instruction,
            BNI to R.string.bni_i_banking_payment_instruction,
            BRI to R.string.bri_i_banking_payment_instruction,
            MANDIRI to R.string.mandiri_i_banking_payment_instruction,
            PERMATA to R.string.permata_i_banking_payment_instruction,
            CIMB to R.string.cimb_i_banking_atm_payment_instruction
        )
        return paymentInstruction[code] ?: R.string.bank_i_banking_payment_instruction

    }

    fun getMBankingPaymentMethodInstruction(code: String): Int {
        val paymentInstruction = mutableMapOf(
            BCA to R.string.bca_m_banking_payment_instruction,
            BNI to R.string.bni_m_banking_payment_instruction,
            BRI to R.string.bri_m_banking_payment_instruction,
            MANDIRI to R.string.mandiri_m_banking_payment_instruction,
            PERMATA to R.string.permata_m_banking_payment_instruction,
            CIMB to R.string.cimb_m_banking_atm_payment_instruction
        )
        return paymentInstruction[code] ?: R.string.bank_m_banking

    }

    fun triggerEvent(
        type: String,
        entryPoint: String,
        transactionId: String? = null,
        action: String? = null,
        eta: String? = null,
        eventName: String
    ) {
        AppAnalytics.PropBuilder().apply {
            put(AnalyticsConst.ENTRY_POINT, entryPoint)
            put(AnalyticsConst.TYPE, type)
            put(AnalyticsConst.TRANSACTION_ID, transactionId)
            if (action.isNotNullOrBlank()) {
                put(AnalyticsConst.ACTION, action)
            }
            AppAnalytics.trackEvent(eventName, this, false, false, false)
        }
    }

    fun updatePaymentCategory(paymentCategoryItem: PaymentCategoryItem) = viewModelScope.launch {
        if (isPaymentIn())
            paymentUseCase.updatePaymentInCategorySelected(
                SessionManager.getInstance().businessId,
                customerId ?: "",
                orderId!!,
                paymentCategoryItem.paymentCategoryId
            )
        else
            paymentUseCase.updatePaymentOutCategorySelected(
                SessionManager.getInstance().businessId,
                orderId!!,
                paymentCategoryItem.paymentCategoryId
            )
    }

    fun getUserProfile(userId: String): UserProfileEntity? {
        return businessUseCase.getUserProfileEntity(userId)
    }

    fun getBankAccounts() = viewModelScope.launch {
        val bookId = SessionManager.getInstance().businessId
        if (!PaymentPrefManager.getInstance().getShowSelectedBankFromLocal()) {
            when (val result = paymentUseCase.getMerchantBankAccounts(bookId)) {
                is ApiSuccessResponse -> {
                    val size = result.body?.size ?: 0
                    FeaturePrefManager.getInstance().setHasBankAccount(size > 0, bookId)
                    PaymentPrefManager.getInstance().setShowSelectedBankFromLocal(true)
                    _selectedBankAccount.value = result.body
                }
            }
        } else {
            _selectedBankAccount.value = paymentUseCase.getLocalMerchantBankAccounts(bookId)
        }
    }

    fun retryDisbursal(bankAccount: BankAccount) = viewModelScope.launch {
        viewState.value = currentViewState().copy(loader = true)
        withContext(Dispatchers.IO) {
            orderId?.let {
                Utilities.safeLet(
                    bankAccount.bankCode,
                    bankAccount.accountNumber,
                    bankAccount.accountHolderName
                ) { bankCode, accountNo, holderName ->
                    val reqBody = DisbursalRequest(bankCode, accountNo, holderName)
                    when (val result = paymentUseCase.retryDisbursal(it, reqBody)) {
                        is ApiSuccessResponse, is ApiEmptyResponse -> {
                            withContext(Dispatchers.Main) {
                                viewState.value = currentViewState().copy(loader = false)
                            }
                            // Refresh screen
                            getOrderDetail(false)
                        }
                        is ApiErrorResponse -> handleApiError(result)
                    }
                }
            }
        }
    }

    fun createInDisbursementAndShareTemplate(bankAccount: BankAccount? = null, context: Context) = viewModelScope.launch {
        bankAccount ?: return@launch
        viewState.value = currentViewState().copy(loader = true)
        val sellingPrice =
            if (orderResponse?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount.orNil > 0.0) orderResponse?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.amount
            else if (orderResponse?.items?.firstOrNull()?.amount.orNil > 0.0) orderResponse?.items?.firstOrNull()?.amount
            else (orderResponse?.amount ?: 0.0) + (orderResponse?.fee ?: 0.0)

        val result = paymentUseCase.requestPayment(
            SessionManager.getInstance().businessId,
            customerId ?: "",
            PaymentCollection.newCollectionRequest(
                amount = sellingPrice!!.toLong(),
                bankAccountId = bankAccount.bankAccountId,
                description = "",
                referenceId = orderResponse?.orderId ?: "",
                customerName = if (customer?.name?.isNotNullOrBlank() == true) customer?.name else orderResponse?.items?.firstOrNull()?.beneficiary?.phoneNumber,
                extras = PaymentExtras(
                    recordIn = PaymentConst.RECORD_IN_DEBT_AND_CASH,
                    accounting = PaymentAccountingExtras(
                        transactionId = orderResponse?.paymentCollectionInfo?.paymentCollectionCashTransactionInfo?.transactionId
                            ?: ""
                    )
                )
            )
        )
        when (result) {
            is ApiSuccessResponse -> {
                message = result.body.template?.getPaymentSharingText(customer)
                init(customerId, orderId, paymentType, false, true)
                AppAnalytics.trackEvent(
                    AnalyticsConst.EVENT_PAYMENT_REQUEST_CREATED,
                    AppAnalytics.PropBuilder().put(AnalyticsConst.ENTRY_POINT, AnalyticsConst.PAYMENT_DETAILS)
                        .put(AnalyticsConst.BANK,bankAccount?.bankCode?:"")
                        .put(AnalyticsConst.INWARDS_AMOUNT,Utility.formatAmount(sellingPrice))
                )
            }
            is ApiErrorResponse -> handleApiError(result)
        }
    }

    fun onContactSelected(_contact: Contact) {
        customerId = _contact.customerId
        contact = _contact
        customer = customerUseCase.getCustomerById(customerId)
        init(customerId, orderId, paymentType)
    }

    private suspend fun <T> handleApiError(response: ApiErrorResponse<T>, showBottomSheet: Boolean = true) {
        withContext(Dispatchers.Main) {
            if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
                if (showBottomSheet) {
                    viewState.value = currentViewState().copy(loader = false)
                    val isServerError =
                        response.errorMessage.isNotNullOrBlank() && message != AppConst.NO_INTERNET_ERROR_MESSAGE
                    productDetail.value = DetailEvent.ApiError(isServerError, response.errorMessage)
                } else {
                    viewState.value = currentViewState().copy(
                        loader = false, serverError = true, internetError = false, retryLoader = false
                    )
                }
            }
            else viewState.value = currentViewState().copy(
                loader = false, serverError = false, internetError = true, retryLoader = false
            )
        }
    }

    fun retryQrisPayment(disbursableId: String) = viewModelScope.launch {
        viewState.value = currentViewState().copy(retryLoader = true)
        when (val response = paymentUseCase.retryQrisPayment(
            SessionManager.getInstance().businessId, disbursableId
        )) {
            is ApiSuccessResponse, is ApiEmptyResponse -> {
                getOrderDetail(false)
                viewState.value = currentViewState().copy(retryLoader = false)
            }
            is ApiErrorResponse -> handleApiError(response)
        }
    }

    fun setQrisBankAccount(bankAccount: BankAccount) = viewModelScope.launch {
        bankAccount.isQrisBank = true
        val bookId = SessionManager.getInstance().businessId
        when (val response = paymentUseCase.addMerchantBankAccount(bookId, bankAccount)) {
            is ApiSuccessResponse -> {
                productDetail.value = DetailEvent.OnQrisBankSet
                PaymentPrefManager.getInstance().updateQrisBankId(bankAccount.bankAccountId)
            }
            is ApiErrorResponse -> handleApiError(response)
        }
    }

    fun removeFavourite(customerProfile: CustomerProfile?, context: Context) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response =
                finproUseCase.deleteFavourite(
                    SessionManager.getInstance().businessId, customerProfile?.favouriteDetails?.id.orEmpty()
                )) {
                is ApiSuccessResponse -> {
                    setEventState(
                        DetailEvent.ShowToast(
                            message = response.body.message ?: context.getString(R.string.remove_favourite_success_msg)
                        )
                    )
                    setEventState(DetailEvent.refreshScreen)
                }
                is ApiErrorResponse -> {
                    setEventState(DetailEvent.ShowToast(message = response.errorMessage))
                }
            }
        }
    }

    private suspend fun setEventState(event: DetailEvent) = withContext(Dispatchers.Main) {
        productDetail.value = event
    }

    fun createContact(name: String, number: String): String? {
        return customerUseCase.saveContactAsCustomer(
            Contact(
                name,
                number,
                "",
                ""
            )
        )
    }

    fun refreshData() {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                SetupManager.getInstance().setSynchedPaymentData(false)
                //AsyncCashTransactionDataSync(false, orderId).execute()
                CashTransactionDataSync(false, orderId ?: "").execute()
                //AsyncPaymentCashTransactionDataSync().execute()
                PaymentCashTransactionDataSync().execute()
            }
        }
    }

    fun getSaldoBalance() = launch {
        when (val result = finproUseCase.getSaldo()) {
            is ApiSuccessResponse -> {
                viewState.value = currentViewState().copy(saldoBalance = result.body.amount)
            }
            else -> {
                // nothing
            }
        }
    }

    fun fetchTransactionDetails(transactionId: String?, cashbackType: String?) = viewModelScope.launch {
        if (transactionId == null) return@launch
        // Fetch transaction detail for PPOB cashbacks
        if (cashbackType == PaymentConst.CASHBACK_TYPE_VOUCHER) return@launch
        val response =
            finproUseCase.getOrderDetail(SessionManager.getInstance().businessId, transactionId, null)
        if (response is ApiSuccessResponse) {
            viewState.value = currentViewState().copy(transactionDetails = response.body)
        }
    }

    fun disableFavFlagOnMerchantLevel() = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            finproUseCase.disableFavFlagOnMerchantLevel(
                SessionManager.getInstance().businessId,
                ADD_TO_FAV_NUDGE,
                DISABLE
            )
        }
    }

    fun disableFavFlagOnTransactionLevel() = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            finproUseCase.disableFavFlagOnTransactionLevel(
                SessionManager.getInstance().businessId,
                orderId ?: "",
                ADD_TO_FAV_NUDGE,
                DISABLE
            )
        }
    }

    fun getTrxOnPendingTimeInMins(): String {
        val startDate = orderResponse?.progress?.firstOrNull { it.state == STATUS_PAID }?.timestamp ?: ""
        return (DateTimeUtils.convertMillisToMins(DateTimeUtils.getCurrentUTCTime() - DateTimeUtils.getTimestampFromUtcDate(startDate))).toString() + " mins"
    }


    fun addFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.addFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    if (response.body.success.isTrue) {
                        setEventState(DetailEvent.ShowToast(stringId = R.string.success_fav_message))
                        setEventState(DetailEvent.refreshScreen)
                    } else {
                        setEventState(DetailEvent.ShowToast(response.body.message))
                    }
                }
                is ApiErrorResponse -> {
                    setEventState(DetailEvent.ShowToast(response.errorMessage))
                }
            }

        }
    }

    fun deleteFavourite(id: String) = viewModelScope.launch {
        withContext(Dispatchers.IO) {
            when (val response = paymentUseCase.removeFavourite(
                SessionManager.getInstance().businessId,
                id
            )) {
                is ApiSuccessResponse -> {
                    if (response.body.success.isTrue) {
                        setEventState(DetailEvent.ShowToast(stringId = R.string.remove_fav_message))
                        setEventState(DetailEvent.refreshScreen)
                    } else {
                        setEventState(DetailEvent.ShowToast(response.body.message.orEmpty()))
                    }
                }
                is ApiErrorResponse -> {
                    setEventState(DetailEvent.ShowToast(response.errorMessage))
                }
            }

        }
    }
}
