package com.bukuwarung.payments.viewmodels

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bukuwarung.activities.BaseViewModel
import com.bukuwarung.analytics.AppAnalytics
import com.bukuwarung.constants.AnalyticsConst
import com.bukuwarung.constants.AppConst
import com.bukuwarung.data.restclient.ApiErrorResponse
import com.bukuwarung.data.restclient.ApiSuccessResponse
import com.bukuwarung.domain.payments.FinproUseCase
import com.bukuwarung.payments.data.model.FlagKey
import com.bukuwarung.payments.data.model.TopupSaldoRequest
import com.bukuwarung.session.SessionManager
import com.bukuwarung.utils.*
import com.bukuwarung.utils.Utilities.safeLet
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import javax.inject.Inject

class TopupSaldoViewModel @Inject constructor(
        private val finproUseCase: FinproUseCase,
        private val sessionManager: SessionManager
) : BaseViewModel() {
    companion object {
        private const val PAYMENT_CHANNEL_BNI = "BNI"
    }

    sealed class Event {
        data class OpenWebView(val url: String, val prop: AppAnalytics.PropBuilder, val paymentMethodCode: String? = null) : Event()
        data class ShowError(val isServerError: Boolean, val message: String? = null, val errorReason: String = "") : Event()
        data class KYCLimitError(val amount: Double, val debitMonthly: Double, val debitMonthlyLimit: Double) : Event()
    }

    data class ViewState(
        val showLoading: Boolean = false,
        val adminFee: Double = 0.0,
        val hideAdminFee: Boolean = true,
        val amount: Double = 0.0,
        val isMinimumError: Boolean = false,
        val minTopupAmount: Double = RemoteConfigUtils.getMinimumTopupSaldoAmount(),
        val hasExistingTopup: Boolean = false,
        val isLimitError: Boolean = false,
        val currentBalance: Double? = 0.0,
        val maxTopupAmount: Double? = null,
        val debitMonthly: Double? = null,
        val debitMonthlyLimit: Double? = null
    )

    private fun currentViewState(): ViewState = viewState.value!!
    val viewState: MutableLiveData<ViewState> = MutableLiveData(ViewState())
    private val eventStatus = MutableLiveData<Event>()
    val observeEvent: LiveData<Event> = eventStatus
    private var existingTopupId: String? = null

    private suspend fun getAdminFee() {
        when (val response = finproUseCase.getSaldoAdminFee()) {
            is ApiSuccessResponse -> {
                setViewState(currentViewState().copy(adminFee = response.body.adminFee ?: 0.0, hideAdminFee = response.body.flags?.get(FlagKey.HIDE_ADMIN_FEE.name).isTrue))
            }
        }
    }

    fun init(fetchAdminFee: Boolean = true) = launch {
        if (fetchAdminFee) getAdminFee()
        checkSaldoBalance()
        checkExisting()
    }

    private fun checkSaldoBalance() = launch {
        when (val result = finproUseCase.getSaldo()) {
            is ApiSuccessResponse -> {
                setViewState(currentViewState().copy(
                    currentBalance = result.body.amount, maxTopupAmount = result.body.limit,
                    debitMonthly = result.body.debitMonthly, debitMonthlyLimit = result.body.debitMonthlyLimit
                ))
            }
            else -> {
                // nothing
            }
        }
    }

    fun onAmountChanged(amt: Long) {
        viewState.value = currentViewState().copy(
            amount = amt.toDouble(),
            isMinimumError = (amt.toDouble() < currentViewState().minTopupAmount),
            isLimitError = isLimitReached(amt.toDouble())
        )
    }

    /**
     * Checks if current amount and current balance becomes more than the max wallet limit
     */
    private fun isLimitReached(amount: Double): Boolean {
        return safeLet(
            currentViewState().maxTopupAmount,
            currentViewState().currentBalance
        ) { maxTopUp, currentBalance ->
            (amount + currentBalance > maxTopUp)
        } ?: run { false }
    }

    private suspend fun checkExisting() {
        withContext(Dispatchers.IO) {
            when (val response = finproUseCase.getExistingSaldoTopup()) {
                is ApiSuccessResponse -> {
                    if (!response.body.isNullOrEmpty()) {
                        existingTopupId = response.body.firstOrNull()?.id
                        if (existingTopupId != null) {
                            setViewState(currentViewState().copy(hasExistingTopup = true))
                        }
                    }
                }
                is ApiErrorResponse -> handleError(response)
                else -> setEventStatus(Event.ShowError(false, errorReason = "Unknown Error"))
            }
        }
        setViewState(currentViewState().copy(showLoading = false))
    }

    fun checkout(shouldCheckKycLimitError: Boolean) = viewModelScope.launch {
        if (currentViewState().amount < currentViewState().minTopupAmount) return@launch

        if (shouldCheckKycLimitError) {
            safeLet(
                currentViewState().debitMonthly,
                currentViewState().debitMonthlyLimit
            ) { debitMonthly, debitMonthlyLimit ->
                if (currentViewState().amount > (debitMonthlyLimit - debitMonthly)) {
                    setEventStatus(
                        Event.KYCLimitError(
                            currentViewState().amount,
                            currentViewState().debitMonthly.orNil,
                            currentViewState().debitMonthlyLimit.orNil
                        )
                    )
                    return@launch
                }
            }
        }

        if (!Utility.hasInternet()) {
            eventStatus.value = Event.ShowError(false, errorReason = AppConst.NO_INTERNET_ERROR_MESSAGE)
            return@launch
        }
        setViewState(currentViewState().copy(showLoading = true))
        withContext(Dispatchers.IO) {
            val request = TopupSaldoRequest(
                    cancelUnpaidTopupId = existingTopupId,
                    amount = currentViewState().amount,
                    accountId = sessionManager.businessId,
                    paymentMethods = listOf()
            )
            when (val response = finproUseCase.topupSaldo(request)) {
                is ApiSuccessResponse -> {
                    if (!response.body.payments.isNullOrEmpty()) {
                        val paymentMethodCode = response.body.payments.firstOrNull()?.paymentMethod?.code
                        val prop = AppAnalytics.PropBuilder().apply {
                            put(AnalyticsConst.AMOUNT, currentViewState().amount)
                        }
                        setEventStatus(Event.OpenWebView(response.body.payments.firstOrNull()?.paymentUrl
                                ?: "", prop, paymentMethodCode))
                    }
                    setViewState(currentViewState().copy(showLoading = false))
                }
                is ApiErrorResponse -> {
                    handleError(response)
                }
            }
        }
    }

    private suspend fun handleError(response: ApiErrorResponse<*>) {
        if (response.errorMessage != AppConst.NO_INTERNET_ERROR_MESSAGE) {
            setViewState(currentViewState().copy(showLoading = false))
            setEventStatus(Event.ShowError(true, response.errorMessage, response.errorMessage))
        } else {
            setViewState(currentViewState().copy(showLoading = false))
            setEventStatus(Event.ShowError(false, errorReason = AppConst.NO_INTERNET_ERROR_MESSAGE))
        }
    }

    private suspend fun setEventStatus(event: Event) = withContext(Dispatchers.Main) {
        eventStatus.value = event
    }

    private suspend fun setViewState(state: ViewState) = withContext(Dispatchers.Main) {
        viewState.value = state
    }
}
